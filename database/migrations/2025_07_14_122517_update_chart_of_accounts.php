<?php

use App\Enums\Finance\AccountCategory;
use App\Models\Finance\CashAccount;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('cash_accounts')
            ->where('category', 'cash')
            ->orWhere('category', 'bank')
            ->update(['category' => 'cash_bank']);

        CashAccount::query()->create([
            'code' => '1.103.003', 'category' => AccountCategory::AccountsReceivable, 'name' => 'Debit Note', 'is_fixed' => true,
        ]);
        CashAccount::query()->create([
            'code' => '2.101.003', 'category' => AccountCategory::AccountsPayable, 'name' => 'Credit Note', 'is_fixed' => true,
        ]);

        CashAccount::query()
            ->where('code', 'like', '1.104.%')
            ->update(['category' => AccountCategory::Inventory]);

        CashAccount::query()
            ->where('code', 'like', '1.206.%')
            ->update(['category' => AccountCategory::AccumulatedDepreciation]);

        CashAccount::query()
            ->where('code', 'like', '7.000.%')
            ->update(['category' => AccountCategory::CostOfSales]);

        CashAccount::query()
            ->where('code', 'like', '8.000.%')
            ->update(['category' => AccountCategory::NonOperatingExpense]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
