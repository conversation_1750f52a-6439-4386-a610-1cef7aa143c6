<?php

use App\Models\Bill;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bills', function (Blueprint $table) {
            $table->json('attachments')->nullable()->after('attachment');
        });

        Bill::query()
            ->whereNotNull('attachment')
            ->get()
            ->each(function ($bill) {
                $bill->update([
                    'attachments' => [$bill->attachment],
                ]);
            });

        Schema::table('bills', function (Blueprint $table) {
            $table->dropColumn('attachment');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bills', function (Blueprint $table) {
            $table->string('attachment')->nullable()->after('attachments');
        });

        Bill::query()
            ->whereNotNull('attachments')
            ->get()
            ->each(function ($bill) {
                $bill->update([
                    'attachment' => $bill->attachments[0] ?? null,
                ]);
            });

        Schema::table('bills', function (Blueprint $table) {
            $table->dropColumn('attachments');
        });
    }
};
