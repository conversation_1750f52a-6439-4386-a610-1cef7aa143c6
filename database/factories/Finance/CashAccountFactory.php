<?php

namespace Database\Factories\Finance;

use App\Enums\Finance\AccountCategory;
use App\Models\Finance\CashAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Finance\CashAccount>
 */
class CashAccountFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CashAccount::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'code' => fake()->unique()->numerify('#.###.###'),
            'category' => fake()->randomElement(AccountCategory::cases()),
            'name' => fake()->words(3, true),
            'description' => fake()->sentence(),
            'is_fixed' => false,
        ];
    }

    /**
     * Configure the factory to create a cash account with a specific category.
     *
     * @return static
     */
    public function category(AccountCategory $category)
    {
        return $this->state(function (array $attributes) use ($category) {
            return [
                'category' => $category,
            ];
        });
    }

    /**
     * Configure the factory to create a cash or bank account.
     *
     * @return static
     */
    public function cashOrBank()
    {
        return $this->state(function (array $attributes) {
            return [
                'category' => fake()->randomElement([AccountCategory::CashBank]),
            ];
        });
    }

    /**
     * Configure the factory to create a receivable account.
     *
     * @return static
     */
    public function receivable()
    {
        return $this->state(function (array $attributes) {
            return [
                'code' => config('finance.coa.receivable'),
                'category' => AccountCategory::AccountsReceivable,
                'name' => 'Piutang Usaha',
                'is_fixed' => true,
            ];
        });
    }

    /**
     * Configure the factory to create a sales account.
     *
     * @return static
     */
    public function sales()
    {
        return $this->state(function (array $attributes) {
            return [
                'code' => config('finance.coa.sales'),
                'category' => AccountCategory::OperatingRevenue,
                'name' => 'Penjualan',
                'is_fixed' => true,
            ];
        });
    }

    /**
     * Configure the factory to create a sales returns account.
     *
     * @return static
     */
    public function salesReturns()
    {
        return $this->state(function (array $attributes) {
            return [
                'code' => config('finance.coa.sales_returns'),
                'category' => AccountCategory::OperatingRevenue,
                'name' => 'Retur Penjualan',
                'is_fixed' => true,
            ];
        });
    }

    /**
     * Configure the factory to create a main cash account.
     *
     * @return static
     */
    public function mainCash()
    {
        return $this->state(function (array $attributes) {
            return [
                'code' => config('finance.coa.main_cash'),
                'category' => AccountCategory::CashBank,
                'name' => 'Kas Besar',
                'is_fixed' => true,
            ];
        });
    }

    /**
     * Configure the factory to create a customer deposit account.
     *
     * @return static
     */
    public function customerDeposit()
    {
        return $this->state(function (array $attributes) {
            return [
                'code' => config('finance.coa.customer_deposit'),
                'category' => AccountCategory::CurrentLiability,
                'name' => 'Deposit Customer',
                'is_fixed' => true,
            ];
        });
    }
}
