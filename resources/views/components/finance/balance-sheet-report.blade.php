@props(['data', 'period'])

<div class="space-y-6">
    <div class="space-y-2 text-center">
        <h1 class="text-2xl font-bold">Balance Sheet</h1>
        <p>Period: {{ \Carbon\Carbon::parse($period['start'])->format('d M Y') }} - {{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}</p>
    </div>

    {{-- Assets --}}
    <div class="space-y-4">
        <h2 class="text-xl font-bold">Assets</h2>

        @php
            $totalAssets = 0;
        @endphp

        @foreach($data['assets'] as $group => $accounts)
            <div class="space-y-2">
                <div class="font-medium">{{ $group }}</div>
                @foreach($accounts as $account)
                    @php
                        $totalAssets += $account['amount'];
                    @endphp
                    <div class="flex justify-between pl-4">
                        <span>
                            @if(isset($account['code']))
                                <span class="text-gray-600 font-mono text-sm">{{ $account['code'] }}</span> -
                            @endif
                            {{ $account['name'] }}
                        </span>
                        <span class="tabular-nums">{{ money($data['exchange_rate'] * $account['amount'], $data['currency_code'], true) }}</span>
                    </div>
                @endforeach
            </div>
        @endforeach

        <div class="flex justify-between pt-2 font-medium border-t">
            <span>Total Assets</span>
            <span class="tabular-nums">{{ money($data['exchange_rate'] * $totalAssets, $data['currency_code'], true) }}</span>
        </div>
    </div>

    {{-- Liabilities --}}
    <div class="space-y-4">
        <h2 class="text-xl font-bold">Liabilities</h2>

        @php
            $totalLiabilities = 0;
        @endphp

        @if(count($data['liabilities']) > 0)
            @foreach($data['liabilities'] as $group => $accounts)
                <div class="space-y-2">
                    <div class="font-medium">{{ $group }}</div>
                    @foreach($accounts as $account)
                        @php
                            $totalLiabilities += $account['amount'];
                        @endphp
                        <div class="flex justify-between pl-4">
                            <span>
                                @if(isset($account['code']))
                                    <span class="text-gray-600 font-mono text-sm">{{ $account['code'] }}</span> -
                                @endif
                                {{ $account['name'] }}
                            </span>
                            <span class="tabular-nums">{{ money($data['exchange_rate'] * $account['amount'], $data['currency_code'], true) }}</span>
                        </div>
                    @endforeach
                </div>
            @endforeach
        @else
            <div class="italic text-gray-500">No liabilities found</div>
        @endif

        <div class="flex justify-between pt-2 font-medium border-t">
            <span>Total Liabilities</span>
            <span class="tabular-nums">{{ money($data['exchange_rate'] * $totalLiabilities, $data['currency_code'], true) }}</span>
        </div>
    </div>

    {{-- Equity --}}
    <div class="space-y-4">
        <h2 class="text-xl font-bold">Equity</h2>

        @php
            $totalEquity = 0;
        @endphp

        @if(count($data['equity']) > 0)
            @foreach($data['equity'] as $group => $accounts)
                <div class="space-y-2">
                    <div class="font-medium">{{ $group }}</div>
                    @foreach($accounts as $account)
                        @php
                            $totalEquity += $account['amount'];
                        @endphp
                        <div class="flex justify-between pl-4">
                            <span>
                                @if(isset($account['code']))
                                    <span class="text-gray-600 font-mono text-sm">{{ $account['code'] }}</span> -
                                @endif
                                {{ $account['name'] }}
                            </span>
                            <span class="tabular-nums">{{ money($data['exchange_rate'] * $account['amount'], $data['currency_code'], true) }}</span>
                        </div>
                    @endforeach
                </div>
            @endforeach
        @else
            <div class="italic text-gray-500">No equity accounts found</div>
        @endif

        <div class="flex justify-between pt-2 font-medium border-t">
            <span>Total Equity</span>
            <span class="tabular-nums">{{ money($data['exchange_rate'] * $totalEquity, $data['currency_code'], true) }}</span>
        </div>
    </div>

    {{-- Total Liabilities and Equity --}}
    <div class="flex justify-between pt-4 text-lg font-bold border-t-2">
        <span>Total Liabilities and Equity</span>
        <span class="tabular-nums">{{ money($data['exchange_rate'] * ($totalLiabilities + $totalEquity), $data['currency_code'], true) }}</span>
    </div>
</div>
