@php
    use App\Enums\InvoiceStatus;
@endphp

@props([
    'record' => null,
    'isInline' => false
])

@if (!$isInline)
<style>
    html {
        color: var(--arm-blue);
        font-size: 10pt;
    }

    #bill-page .page-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
    }

    #bill-page .footer-space {
        height: 1cm;
    }

    @media screen {
        body {
            position: relative;
        }

        #bill-page .page-footer {
            left: 1cm;
            right: 1cm;
            padding-bottom: 1cm;
            position: absolute;
        }
    }
</style>
@endif
<style>
    :root {
        --arm-blue: #1a2b48;
    }

    #bill-page .page-footer {
        border-top: 1px solid #ddd;
        font-size: 8pt;
        font-style: italic;
        padding-top: 0.1cm;
    }

    #bill-page .w-full {
        width: 100%;
    }

    #bill-page .bill-table th {
        background-color: var(--arm-blue);
        color: white;
        font-weight: normal;
        padding: .5rem 1rem;
    }

    #bill-page .bill-table td {
        padding: .5rem 1rem;
        vertical-align: top;
        page-break-inside: avoid;
    }

    #bill-page .bill-table tr.bordered td {
        border-bottom: 1px solid #ddd;
    }

    #bill-page .flex {
        display: flex;
    }

    #bill-page .flex-col {
        flex-direction: column;
    }

    #bill-page .grow {
        flex-grow: 1;
    }

    #bill-page .items-start {
        align-items: flex-start;
    }

    #bill-page .items-end {
        align-items: flex-end;
    }

    #bill-page .justify-content-end {
        justify-content: end;
    }

    #bill-page .text-right {
        text-align: right;
    }

    #bill-page .logo {
        display: block;
        height: 1.5cm;
        width: auto;
    }

    #bill-page .currency {
        font-variant-numeric: tabular-nums;
        white-space: nowrap;
    }

    #bill-page .muted {
        color: #777;
    }

    #bill-page .title {
        font-size: 30pt;
    }

    #bill-page .subtitle {
        color: #666;
        font-weight: bold;
    }

    #bill-page .item-description {
        color: #777;
        font-size: 10pt;
    }
</style>
<div id="bill-page">
    <table class="w-full">
        <tbody><tr><td>
            <div class="flex items-start w-full">
                <div class="grow">
                    <h1 class="title">BILL</h1>
                    <h2 class="subtitle"># {{ $record->bill_number }}</h2>

                    <div class="flex items-start" style="margin-top: 0.5cm;">
                    @switch($record->status)
                        @case(InvoiceStatus::Paid)
                            <div style="font-weight: bold; color: green; padding: 0.15em 0.65em; border: 2px solid green; font-size: 14pt;">PAID</div>
                            @break
                        @case(InvoiceStatus::PaidPartial)
                            <div style="font-weight: bold; color: orange; padding: 0.15em 0.65em; border: 2px solid orange; font-size: 14pt;">PARTIALLY PAID</div>
                            @break
                        @default
                            <div style="font-weight: bold; color: red; padding: 0.15em 0.65em; border: 2px solid red; font-size: 14pt;">UNPAID</div>
                    @endswitch
                    </div>
                </div>
                @php
                    $company = app(App\Settings\CompanySettings::class);
                @endphp
                <div class="flex flex-col items-end text-right grow">
                    @inlinedImage(asset('images/logo-wide.svg'), 'logo')
                    <p><strong>{{ $company->name }}</strong></p>
                    <div class="muted">
                        {!! nl2br($company->address) !!}<br>
                        {{ $company->email }} | {{ $company->phone }}<br>
                        {{ $company->website }}
                    </div>
                </div>
            </div>

            <div class="flex" style="padding: 0.5cm 0;">
                <div style="width: 25%; line-height: 200%;">
                    <p class="muted">Bill Date :</p>
                    <p class="muted">Due Date :</p>
                    @if ($record->status === InvoiceStatus::Cancelled)
                    <p class="muted">Cancellation Note :</p>
                    @endif
                </div>
                <div style="width: 35%; line-height: 200%;">
                    <p>{{ $record->bill_date->format('j M Y') }}</p>
                    <p>{{ $record->due_date->format('j M Y') }}</p>
                    @if ($record->status === InvoiceStatus::Cancelled)
                    <p>{{ $record->cancellation_note }}</p>
                    @endif
                </div>
                <div style="width: 40%">
                    <p class="muted">Vendor</p>
                    @if ($record->vendor)
                        <p><strong>{{ $record->vendor->company_name }}</strong></p>
                        @if ($record->vendor->contact_name || $record->vendor->contact_phone)
                        <div>
                            {{ $record->vendor->contact_name }}
                            @if ($record->vendor->contact_phone)
                            ({{ $record->vendor->contact_phone }})
                            @endif
                        </div>
                        @endif
                        {{-- <div>{{ $record->vendor->address }}</div> --}}
                    @else
                        <p><strong>[vendor dihapus]</strong></p>
                    @endif
                </div>
            </div>

            <div style="padding: 0.5cm 0;">
                <p class="muted">Subject :</p>
                <p>{{ $record->subject }}</p>
            </div>

            <table class="w-full bill-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Item & Description</th>
                        <th class="text-right">Qty</th>
                        <th class="text-right">Rate</th>
                        <th class="text-right">VAT</th>
                        <th class="text-right">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($record->items as $item)
                        <tr class="bordered">
                            <td>{{ $loop->index + 1 }}</td>
                            <td>
                                <p>{{ $item->name }}</p>
                                <div class="item-description">{!! nl2br($item->description) !!}</div>
                            </td>
                            <td class="text-right">{{ $item->quantity }}</td>
                            <td class="text-right currency">{{ number_format($item->unit_price, 2, '.', ',') }}</td>
                            <td class="text-right currency">{{ $item->vat }}%</td>
                            <td class="text-right currency">
                                {{ number_format($item->unit_price * $item->quantity * (1 + $item->vat / 100), 2, '.', ',') }}
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            <div class="flex justify-content-end">
                <div style="width: 60%">
                    @php
                        $total = $record->total;
                        $payments = $record->payments;
                        $totalPayment = 0;
                    @endphp
                    <table class="w-full bill-table">
                        <tbody>
                            <tr>
                                <td class="text-right">Sub Total</td>
                                <td class="text-right currency">{{ money($total, $record->currency_code, true) }}</td>
                            </tr>
                            @foreach ($payments as $payment)
                            @php $totalPayment += $payment->amount_converted; @endphp
                            <tr>
                                <td class="text-right">Payment on {{ $payment->paid_at->format('j M Y') }}</td>
                                <td class="text-right currency">
                                    {{ money($payment->amount_converted, $record->currency_code, true) }}
                                </td>
                            </tr>
                            @endforeach
                            <tr style="background-color: #f2f2f2">
                                <td class="text-right"><strong>Balance Due</strong></td>
                                <td class="text-right currency">
                                    <strong>{{ money($total - $totalPayment, $record->currency_code, true) }}</strong>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div style="margin-top: 0.75cm;">
                <p class="muted">Notes</p>
                <div style="font-size: 9pt; margin-top: 0.25cm;">{!! nl2br($record->notes) !!}</div>
            </div>
        </td></tr></tbody>
        <tfoot><tr><td>
            <div class="footer-space">&nbsp;</div>
        </td></tr></tfoot>
    </table>

    {{-- <div class="page-footer" style="margin-top: 0.5cm;">
        <p>Thank you for choosing {{ $company->name }} for hotel reservation and handling service.</p>
    </div> --}}
</div>
