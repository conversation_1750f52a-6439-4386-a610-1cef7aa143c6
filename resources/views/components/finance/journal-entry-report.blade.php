@props(['data', 'period'])

<div class="space-y-6">
    <div class="space-y-2 text-center">
        <h1 class="text-2xl font-bold">Journal Entry Report</h1>
        <p>Period: {{ \Carbon\Carbon::parse($period['start'])->format('d M Y') }} - {{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}</p>
        @if($data['selected_account_id'])
            <p class="text-sm text-gray-600">Filtered for specific account</p>
        @endif
    </div>

    @if(count($data['entries']) > 0)
        {{-- Summary Section --}}
        <div class="grid grid-cols-2 gap-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
            <div class="text-center">
                <div class="text-sm text-gray-600">Total Debits</div>
                <div class="text-lg font-semibold tabular-nums">
                    {{ money($data['exchange_rate'] * $data['total_debits'], $data['currency_code'], true) }}
                </div>
            </div>
            <div class="text-center">
                <div class="text-sm text-gray-600">Total Credits</div>
                <div class="text-lg font-semibold tabular-nums">
                    {{ money($data['exchange_rate'] * $data['total_credits'], $data['currency_code'], true) }}
                </div>
            </div>
        </div>

        {{-- Journal Entries --}}
        @foreach($data['entries'] as $entry)
            <div class="p-4 space-y-4 border border-gray-200 rounded-lg">
                {{-- Entry Header --}}
                <div class="space-y-2">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold">
                            Journal Entry #{{ $entry['entry_id'] }}
                        </h3>
                        <span class="text-sm text-gray-600">
                            {{ \Carbon\Carbon::parse($entry['entry_date'])->format('d M Y') }}
                        </span>
                    </div>

                    <div class="space-y-1">
                        <p class="text-sm text-gray-700">{{ $entry['details'] }}</p>
                        @if($entry['transaction_type'] && $entry['transaction_number'])
                            <p class="text-xs text-gray-500">
                                Reference: {{ $entry['transaction_type'] }} #{{ $entry['transaction_number'] }}
                            </p>
                        @endif
                    </div>
                </div>

                {{-- Entry Items Table --}}
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="border-b bg-gray-50">
                                <th class="px-3 py-2 text-left">Account Code</th>
                                <th class="px-3 py-2 text-left">Account Name</th>
                                <th class="px-3 py-2 text-right">Debit</th>
                                <th class="px-3 py-2 text-right">Credit</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($entry['items'] as $item)
                                <tr class="border-b hover:bg-gray-50">
                                    <td class="px-3 py-2 font-medium">{{ $item['account_code'] }}</td>
                                    <td class="px-3 py-2">{{ $item['account_name'] }}</td>
                                    <td class="px-3 py-2 text-right tabular-nums">
                                        @if($item['debit'] > 0)
                                            {{ number_format($item['debit'] * $data['exchange_rate'], 2) }}
                                        @endif
                                    </td>
                                    <td class="px-3 py-2 text-right tabular-nums">
                                        @if($item['credit'] > 0)
                                            {{ number_format($item['credit'] * $data['exchange_rate'], 2) }}
                                        @endif
                                    </td>
                                </tr>
                            @endforeach

                            {{-- Entry Totals --}}
                            <tr class="font-medium border-t-2 bg-blue-50">
                                <td class="px-3 py-2" colspan="2">Entry Total</td>
                                <td class="px-3 py-2 font-semibold text-right tabular-nums">
                                    {{ number_format($entry['entry_debits'] * $data['exchange_rate'], 2) }}
                                </td>
                                <td class="px-3 py-2 font-semibold text-right tabular-nums">
                                    {{ number_format($entry['entry_credits'] * $data['exchange_rate'], 2) }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                {{-- Entry Balance Check --}}
                @php
                    $balanceDifference = abs($entry['entry_debits'] - $entry['entry_credits']);
                @endphp
                @if($balanceDifference > 0.001)
                    <div class="p-3 text-sm text-red-700 bg-red-100 border border-red-200 rounded">
                        <strong>Warning:</strong> This entry is not balanced.
                        Difference: {{ money($data['exchange_rate'] * $balanceDifference, $data['currency_code'], true) }}
                    </div>
                @else
                    <div class="p-2 text-xs text-green-700 bg-green-100 border border-green-200 rounded">
                        ✓ Entry is balanced
                    </div>
                @endif
            </div>
        @endforeach

        {{-- Grand Totals --}}
        <div class="p-4 border-2 border-gray-300 rounded-lg bg-gray-50">
            <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                    <div class="text-sm text-gray-600">Grand Total Debits</div>
                    <div class="text-xl font-bold tabular-nums">
                        {{ money($data['exchange_rate'] * $data['total_debits'], $data['currency_code'], true) }}
                    </div>
                </div>
                <div>
                    <div class="text-sm text-gray-600">Grand Total Credits</div>
                    <div class="text-xl font-bold tabular-nums">
                        {{ money($data['exchange_rate'] * $data['total_credits'], $data['currency_code'], true) }}
                    </div>
                </div>
                <div>
                    <div class="text-sm text-gray-600">Difference</div>
                    @php
                        $totalDifference = abs($data['total_debits'] - $data['total_credits']);
                    @endphp
                    <div class="text-xl font-bold tabular-nums {{ $totalDifference > 0.001 ? 'text-red-600' : 'text-green-600' }}">
                        {{ money($data['exchange_rate'] * $totalDifference, $data['currency_code'], true) }}
                    </div>
                </div>
            </div>

            @if($totalDifference > 0.001)
                <div class="p-3 mt-3 text-sm text-red-700 bg-red-100 border border-red-200 rounded">
                    <strong>Warning:</strong> Total debits and credits do not match. Please review the journal entries.
                </div>
            @else
                <div class="p-2 mt-3 text-sm text-center text-green-700 bg-green-100 border border-green-200 rounded">
                    ✓ All entries are balanced
                </div>
            @endif
        </div>
    @else
        <div class="py-12 text-center text-gray-500">
            <div class="text-lg font-medium">No journal entries found</div>
            <p class="mt-2">No journal entries were found for the selected period and criteria.</p>
        </div>
    @endif
</div>
