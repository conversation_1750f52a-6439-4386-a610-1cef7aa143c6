@props([
    'deposit' => null,
    'isInline' => false,
])

@php
    $company = app(App\Settings\CompanySettings::class);
@endphp

@if (! $isInline)
    <style>
        html {
            color: var(--arm-blue);
            font-size: 9pt;
        }

        #receipt-page .text-sm {
            font-size: 9pt;
            line-height: 1.25rem;
        }

        #receipt-page .page-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
        }

        @media screen {
            body {
                position: relative;
            }
            #receipt-page .page-footer {
                left: 1cm;
                right: 1cm;
                padding-bottom: 1cm;
                position: absolute;
            }
        }
    </style>
@endif

<style>
    :root {
        --arm-blue: #1a2b48;
    }
    #receipt-page .page-footer {
        border-top: 1px solid #ddd;
        font-size: 8pt;
        font-style: italic;
        padding-top: 0.1cm;
    }
    #receipt-page .w-full {
        width: 100%;
    }
    #receipt-page .font-bold {
        font-weight: bold;
    }
    #receipt-page table th {
        background-color: var(--arm-blue);
        color: white;
        font-weight: normal;
        padding: 0.5rem 1rem;
    }
    #receipt-page table td {
        padding: 0.5rem 1rem;
        vertical-align: top;
    }
    #receipt-page table tr.bordered td {
        border-bottom: 1px solid #ddd;
    }
    #receipt-page .flex {
        display: flex;
    }
    #receipt-page .flex-col {
        flex-direction: column;
    }
    #receipt-page .grow {
        flex-grow: 1;
    }
    #receipt-page .items-start {
        align-items: flex-start;
    }
    #receipt-page .items-end {
        align-items: flex-end;
    }
    #receipt-page .justify-content-end {
        justify-content: end;
    }
    #receipt-page .text-right {
        text-align: right;
    }
    #receipt-page .logo {
        display: block;
        height: 1.5cm;
        width: auto;
    }
    #receipt-page .currency {
        font-variant-numeric: tabular-nums;
        white-space: nowrap;
    }
    #receipt-page .muted {
        color: #777;
    }
    #receipt-page .title {
        font-size: 36pt;
    }
    #receipt-page .subtitle {
        color: #666;
        font-weight: bold;
    }
    #receipt-page .item-description {
        color: #777;
        font-size: 10pt;
    }
</style>
<div id="receipt-page">
    <div class="flex items-start w-full">
        <div class="grow">
            <h1 class="title">RECEIPT</h1>
            <h2 class="subtitle">Deposit# {{ str_pad($deposit->id, 6, '0', STR_PAD_LEFT) }}</h2>
        </div>
        <div class="flex flex-col items-end text-right grow">
            @inlinedImage(asset('images/logo-wide.svg'), 'logo')
            <p><strong>{{ $company->name }}</strong></p>
            <div class="muted">
                {!! nl2br($company->address) !!}
                <br />
                {{ $company->email }} | {{ $company->phone }}
                <br />
                {{ $company->website }}
            </div>
        </div>
    </div>

    <div class="flex" style="padding: 0.5cm 0">
        <div style="width: 25%; line-height: 200%">
            <p class="muted">Date :</p>
            <p class="muted">Cashier :</p>
        </div>
        <div style="width: 35%; line-height: 200%">
            <p>{{ $deposit->cashed_at->format('j M Y') }}</p>
            <p>{{ $deposit->created_by?->name }}</p>
        </div>
        <div style="width: 40%">
            <p class="muted">Received From</p>
            <p><strong>{{ $deposit->customer->name }}</strong></p>
            @if ($deposit->customer->owner_name || $deposit->customer->phone)
                <div>
                    {{ $deposit->customer->owner_name }}
                    @if ($deposit->customer->phone)
                        ({{ $deposit->customer->phone }})
                    @endif
                </div>
            @endif

            <div>{{ $deposit->customer->address }}</div>
        </div>
    </div>

    <table class="w-full">
        <thead>
            <tr>
                <th class="text-left">Description</th>
                <th class="text-left">Via</th>
                <th class="text-right">Amount</th>
            </tr>
        </thead>
        <tbody>
            <tr class="bordered">
                <td>
                    <p>{{ $deposit->details }}</p>
                </td>
                <td>
                    @php
                        $category = $deposit->cashAccount?->category;
                    @endphp

                    <p>{{ $category?->getLabel() }}</p>
                    @if ((bool) $deposit->cashAccount->description)
                        <p class="text-sm font-bold">{{ $deposit->cashAccount->name }}</p>
                        <p class="text-sm muted">{{ $deposit->cashAccount->description }}</p>
                    @endif
                </td>
                <td class="text-right currency">{{ money($deposit->amount, $deposit->currency, true) }}</td>
            </tr>
        </tbody>
    </table>
    <div class="flex justify-content-end">
        <div style="width: 60%">
            <table class="w-full">
                <tbody>
                    <tr style="background-color: #f2f2f2">
                        <td class="text-right"><strong>Total Deposit</strong></td>
                        <td class="text-right currency">
                            <strong>{{ money($deposit->amount, $deposit->currency, true) }}</strong>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="page-footer" style="margin-top: 0.5cm">
        <p>Thank you for your payment.</p>
    </div>
</div>
