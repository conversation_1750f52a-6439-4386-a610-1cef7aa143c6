@props(['data', 'period'])

<div class="space-y-6">
    <div class="space-y-2 text-center">
        <h1 class="text-2xl font-bold">Advance Cash Report</h1>
        <p>Period: {{ \Carbon\Carbon::parse($period['start'])->format('d M Y') }} - {{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}</p>
        @if($data['selected_user_id'])
            <p class="text-sm text-gray-600">Filtered for specific user</p>
        @endif
    </div>

    @if(count($data['users']) > 0)
        @foreach($data['users'] as $userData)
            <div class="p-4 space-y-4 border border-gray-200 rounded-lg">
                {{-- User Header --}}
                <div class="space-y-2">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-semibold">
                            {{ $userData['user']['name'] }}
                        </h2>
                        <span class="px-2 py-1 text-sm text-gray-600 bg-gray-100 rounded">
                            Advance Cash Account
                        </span>
                    </div>

                    @if($userData['opening_balance'] != 0)
                        <div class="text-sm text-gray-600">
                            Opening Balance:
                            <span class="font-medium tabular-nums {{ $userData['opening_balance'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                {{ money($data['exchange_rate'] * $userData['opening_balance'], $data['currency_code'], true) }}
                            </span>
                        </div>
                    @endif
                </div>

                {{-- Transactions Table --}}
                @if(count($userData['transactions']) > 0)
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead>
                                <tr class="border-b bg-gray-50">
                                    <th class="px-3 py-2 text-left">Date</th>
                                    <th class="px-3 py-2 text-left">Description</th>
                                    <th class="px-3 py-2 text-left">Ref #</th>
                                    <th class="px-3 py-2 text-right">Advance (Debit)</th>
                                    <th class="px-3 py-2 text-right">Return/Expense (Credit)</th>
                                    <th class="px-3 py-2 text-right">Balance</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{-- Opening Balance Row --}}
                                @if($userData['opening_balance'] != 0)
                                    <tr class="border-b bg-blue-50">
                                        <td class="px-3 py-2 text-gray-600 whitespace-nowrap">{{ \Carbon\Carbon::parse($period['start'])->subDay()->format('d M Y') }}</td>
                                        <td class="px-3 py-2 font-medium text-blue-700">Opening Balance</td>
                                        <td class="px-3 py-2"></td>
                                        <td class="px-3 py-2 text-right"></td>
                                        <td class="px-3 py-2 text-right"></td>
                                        <td class="px-3 py-2 font-medium text-right tabular-nums {{ $userData['opening_balance'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                            {{ money($data['exchange_rate'] * $userData['opening_balance'], $data['currency_code'], true) }}
                                        </td>
                                    </tr>
                                @endif

                                {{-- Transaction Rows --}}
                                @foreach($userData['transactions'] as $transaction)
                                    <tr class="border-b hover:bg-gray-50">
                                        <td class="px-3 py-2 whitespace-nowrap">{{ \Carbon\Carbon::parse($transaction['date'])->format('d M Y') }}</td>
                                        <td class="px-3 py-2">{{ $transaction['details'] }}</td>
                                        <td class="px-3 py-2 text-sm text-gray-600">{{ $transaction['ref_number'] }}</td>
                                        <td class="px-3 py-2 text-right tabular-nums">
                                            @if($transaction['debit'] > 0)
                                                <span class="font-medium text-green-600">
                                                    {{ number_format($transaction['debit'] * $data['exchange_rate'], 2) }}
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-3 py-2 text-right tabular-nums">
                                            @if($transaction['credit'] > 0)
                                                <span class="font-medium text-red-600">
                                                    {{ number_format($transaction['credit'] * $data['exchange_rate'], 2) }}
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-3 py-2 font-medium text-right tabular-nums {{ $transaction['balance'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                            {{ money($data['exchange_rate'] * $transaction['balance'], $data['currency_code'], true) }}
                                        </td>
                                    </tr>
                                @endforeach

                                {{-- Closing Balance Row --}}
                                <tr class="font-medium border-t-2 bg-green-50">
                                    <td class="px-3 py-2 text-gray-600">{{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}</td>
                                    <td class="px-3 py-2 font-semibold text-green-700">Closing Balance</td>
                                    <td class="px-3 py-2"></td>
                                    <td class="px-3 py-2 text-right"></td>
                                    <td class="px-3 py-2 text-right"></td>
                                    <td class="px-3 py-2 font-semibold text-right tabular-nums {{ $userData['closing_balance'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                        {{ money($data['exchange_rate'] * $userData['closing_balance'], $data['currency_code'], true) }}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="py-8 text-center text-gray-500">
                        <p>No transactions found for this user in the selected period.</p>
                        @if($userData['opening_balance'] != 0)
                            <p class="mt-2">
                                User balance remains:
                                <span class="font-medium tabular-nums {{ $userData['opening_balance'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                    {{ money($data['exchange_rate'] * $userData['opening_balance'], $data['currency_code'], true) }}
                                </span>
                            </p>
                        @endif
                    </div>
                @endif

                {{-- User Summary --}}
                @if(count($userData['transactions']) > 0)
                    @php
                        $totalAdvances = collect($userData['transactions'])->sum('debit');
                        $totalReturns = collect($userData['transactions'])->sum('credit');
                        $netChange = $userData['closing_balance'] - $userData['opening_balance'];
                    @endphp
                    <div class="grid grid-cols-4 gap-4 p-3 pt-4 border-t rounded bg-gray-50">
                        <div class="text-center">
                            <div class="text-xs text-gray-600">Total Advances</div>
                            <div class="font-medium text-green-600 tabular-nums">{{ money($data['exchange_rate'] * $totalAdvances, $data['currency_code'], true) }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xs text-gray-600">Total Returns/Expenses</div>
                            <div class="font-medium text-red-600 tabular-nums">{{ money($data['exchange_rate'] * $totalReturns, $data['currency_code'], true) }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xs text-gray-600">Net Change</div>
                            <div class="font-medium tabular-nums {{ $netChange >= 0 ? 'text-green-600' : 'text-red-600' }}">{{ money($data['exchange_rate'] * $netChange, $data['currency_code'], true) }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xs text-gray-600">Outstanding Balance</div>
                            <div class="font-medium tabular-nums {{ $userData['closing_balance'] >= 0 ? 'text-green-600' : 'text-red-600' }}">{{ money($data['exchange_rate'] * $userData['closing_balance'], $data['currency_code'], true) }}</div>
                        </div>
                    </div>
                @endif
            </div>
        @endforeach

        {{-- Overall Summary --}}
        @if(count($data['users']) > 1)
            @php
                $totalOpeningBalance = collect($data['users'])->sum('opening_balance');
                $totalClosingBalance = collect($data['users'])->sum('closing_balance');
                $totalAdvances = collect($data['users'])->sum(function($user) {
                    return collect($user['transactions'])->sum('debit');
                });
                $totalReturns = collect($data['users'])->sum(function($user) {
                    return collect($user['transactions'])->sum('credit');
                });
            @endphp
            <div class="p-4 space-y-4 border-2 border-blue-200 rounded-lg bg-blue-50">
                <h3 class="text-lg font-semibold text-blue-800">Overall Summary</h3>
                <div class="grid grid-cols-2 gap-4 md:grid-cols-4">
                    <div class="text-center">
                        <div class="text-xs text-blue-600">Total Opening Balance</div>
                        <div class="font-medium tabular-nums {{ $totalOpeningBalance >= 0 ? 'text-green-600' : 'text-red-600' }}">
                            {{ money($data['exchange_rate'] * $totalOpeningBalance, $data['currency_code'], true) }}
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="text-xs text-blue-600">Total Advances</div>
                        <div class="font-medium text-green-600 tabular-nums">
                            {{ money($data['exchange_rate'] * $totalAdvances, $data['currency_code'], true) }}
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="text-xs text-blue-600">Total Returns/Expenses</div>
                        <div class="font-medium text-red-600 tabular-nums">
                            {{ money($data['exchange_rate'] * $totalReturns, $data['currency_code'], true) }}
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="text-xs text-blue-600">Total Outstanding</div>
                        <div class="font-medium tabular-nums {{ $totalClosingBalance >= 0 ? 'text-green-600' : 'text-red-600' }}">
                            {{ money($data['exchange_rate'] * $totalClosingBalance, $data['currency_code'], true) }}
                        </div>
                    </div>
                </div>
            </div>
        @endif
    @else
        <div class="py-12 text-center text-gray-500">
            <div class="text-lg font-medium">No advance cash transactions found</div>
            <p class="mt-2">No user cash transactions were found for the selected period and criteria.</p>
        </div>
    @endif
</div>
