@props(['data', 'period'])

<div class="space-y-6">
    <div class="space-y-2 text-center">
        <h1 class="text-2xl font-bold">Sales Performance Report</h1>
        <p>Period: {{ \Carbon\Carbon::parse($period['start'])->format('d M Y') }} - {{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}</p>
        @if($data['customer_filter'])
            <p class="text-sm text-gray-600">Customer: {{ $data['customer_filter'] }}</p>
        @endif
    </div>

    {{-- Summary Metrics --}}
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div class="p-4 border border-blue-200 rounded-lg bg-blue-50">
            <div class="text-sm font-medium text-blue-600">Total Sales</div>
            <div class="text-2xl font-bold text-blue-900">{{ money($data['summary']['total_sales'], $data['currency_code'] ?? 'SAR', true) }}</div>
        </div>
        <div class="p-4 border border-green-200 rounded-lg bg-green-50">
            <div class="text-sm font-medium text-green-600">Total Paid</div>
            <div class="text-2xl font-bold text-green-900">{{ money($data['summary']['total_paid'], $data['currency_code'] ?? 'SAR', true) }}</div>
        </div>
        <div class="p-4 border border-orange-200 rounded-lg bg-orange-50">
            <div class="text-sm font-medium text-orange-600">Outstanding</div>
            <div class="text-2xl font-bold text-orange-900">{{ money($data['summary']['total_outstanding'], $data['currency_code'] ?? 'SAR', true) }}</div>
        </div>
        <div class="p-4 border border-purple-200 rounded-lg bg-purple-50">
            <div class="text-sm font-medium text-purple-600">Payment Rate</div>
            <div class="text-2xl font-bold text-purple-900">{{ number_format($data['summary']['payment_rate'], 1) }}%</div>
        </div>
    </div>

    {{-- Additional Summary Stats --}}
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div class="p-4 border border-gray-200 rounded-lg bg-gray-50">
            <div class="text-sm font-medium text-gray-600">Total Invoices</div>
            <div class="text-xl font-bold text-gray-900">{{ number_format($data['summary']['total_invoices']) }}</div>
        </div>
        <div class="p-4 border border-gray-200 rounded-lg bg-gray-50">
            <div class="text-sm font-medium text-gray-600">Avg Invoice Value</div>
            <div class="text-xl font-bold text-gray-900">{{ money($data['summary']['average_invoice_value'], $data['currency_code'] ?? 'SAR', true) }}</div>
        </div>
        <div class="p-4 border border-gray-200 rounded-lg bg-gray-50">
            <div class="text-sm font-medium text-gray-600">Unique Customers</div>
            <div class="text-xl font-bold text-gray-900">{{ number_format($data['summary']['unique_customers']) }}</div>
        </div>
        <div class="p-4 border border-gray-200 rounded-lg bg-gray-50">
            <div class="text-sm font-medium text-gray-600">Unique Groups</div>
            <div class="text-xl font-bold text-gray-900">{{ number_format($data['summary']['unique_groups']) }}</div>
        </div>
    </div>

    {{-- Sales Data by Group --}}
    <div class="space-y-4">
        <h2 class="text-xl font-semibold">Sales Data by {{ ucfirst($data['group_by']) }}</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            {{ ucfirst($data['group_by']) }}
                        </th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            Invoices
                        </th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            Sales
                        </th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            Paid
                        </th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            Outstanding
                        </th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            Payment Rate
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($data['grouped_data'] as $row)
                        <tr>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">
                                {{ $row['label'] }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ number_format($row['count']) }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ money($row['sales'], $data['currency_code'] ?? 'SAR', true) }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ money($row['paid'], $data['currency_code'] ?? 'SAR', true) }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ money($row['outstanding'], $data['currency_code'] ?? 'SAR', true) }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ $row['sales'] > 0 ? number_format(($row['paid'] / $row['sales']) * 100, 1) : 0 }}%
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    {{-- Top Customers --}}
    <div class="space-y-4">
        <h2 class="text-xl font-semibold">Top 10 Customers by Sales</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            Customer
                        </th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            Sales
                        </th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            Invoices
                        </th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            Payment Rate
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($data['top_customers'] as $customer)
                        <tr>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">
                                {{ $customer['name'] }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ money($customer['sales'], $data['currency_code'] ?? 'SAR', true) }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                {{ number_format($customer['invoices']) }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-16 h-2 mr-2 bg-gray-200 rounded-full">
                                        <div class="h-2 bg-green-600 rounded-full" style="width: {{ $customer['payment_rate'] }}%"></div>
                                    </div>
                                    {{ number_format($customer['payment_rate'], 1) }}%
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    {{-- Invoice Status Breakdown --}}
    <div class="space-y-4">
        <h2 class="text-xl font-semibold">Invoice Status Breakdown</h2>
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            @foreach($data['status_breakdown'] as $status)
                <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
                    <div class="text-sm font-medium text-gray-600">{{ $status['status'] }}</div>
                    <div class="text-xl font-bold text-gray-900">{{ number_format($status['count']) }}</div>
                    <div class="text-sm text-gray-500">{{ number_format($status['percentage'], 1) }}% of total</div>
                    <div class="text-sm text-gray-500">{{ money($status['sales'], $data['currency_code'] ?? 'SAR', true) }}</div>
                </div>
            @endforeach
        </div>
    </div>

    {{-- Payment Trends --}}
    @if(count($data['payment_trends']) > 0)
        <div class="space-y-4">
            <h2 class="text-xl font-semibold">Payment Trends</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                Month
                            </th>
                            <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                Payment Amount
                            </th>
                            <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                Payment Count
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($data['payment_trends'] as $trend)
                            <tr>
                                <td class="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">
                                    {{ $trend['month'] }}
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                    {{ money($trend['amount'], $data['currency_code'] ?? 'SAR', true) }}
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                    {{ number_format($trend['count']) }}
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    @endif
</div>
