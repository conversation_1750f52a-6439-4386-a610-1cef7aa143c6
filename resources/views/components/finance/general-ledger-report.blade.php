@props(['data', 'period'])

<div class="space-y-6">
    <div class="space-y-2 text-center">
        <h1 class="text-2xl font-bold">General Ledger</h1>
        <p>Period: {{ \Carbon\Carbon::parse($period['start'])->format('d M Y') }} - {{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}</p>
        @if($data['selected_account_id'])
            <p class="text-sm text-gray-600">Filtered for specific account</p>
        @endif
    </div>

    @if(count($data['accounts']) > 0)
        @foreach($data['accounts'] as $accountData)
            <div class="p-4 space-y-4 border border-gray-200 rounded-lg">
                {{-- Account Header --}}
                <div class="space-y-2">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-semibold">
                            {{ $accountData['account']['code'] }} - {{ $accountData['account']['name'] }}
                        </h2>
                        <span class="px-2 py-1 text-sm text-gray-600 bg-gray-100 rounded">
                            {{ \App\Enums\Finance\AccountCategory::from($accountData['account']['category'])->getLabel() }}
                        </span>
                    </div>

                    @if($accountData['opening_balance'] != 0)
                        <div class="text-sm text-gray-600">
                            Opening Balance:
                            <span class="font-medium tabular-nums">
                                {{ money($data['exchange_rate'] * $accountData['opening_balance'], $data['currency_code'], true) }}
                            </span>
                        </div>
                    @endif
                </div>

                {{-- Transactions Table --}}
                @if(count($accountData['transactions']) > 0)
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead>
                                <tr class="border-b bg-gray-50">
                                    <th class="px-3 py-2 text-left">Date</th>
                                    <th class="px-3 py-2 text-left">Description</th>
                                    <th class="px-3 py-2 text-left">Ref</th>
                                    <th class="px-3 py-2 text-right">Ref #</th>
                                    <th class="px-3 py-2 text-right">Debit</th>
                                    <th class="px-3 py-2 text-right">Credit</th>
                                    <th class="px-3 py-2 text-right">Balance</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{-- Opening Balance Row --}}
                                @if($accountData['opening_balance'] != 0)
                                    <tr class="border-b bg-blue-50">
                                        <td class="px-3 py-2 text-gray-600 whitespace-nowrap">{{ \Carbon\Carbon::parse($period['start'])->subDay()->format('d M Y') }}</td>
                                        <td class="px-3 py-2 font-medium text-blue-700">Opening Balance</td>
                                        <td class="px-3 py-2"></td>
                                        <td class="px-3 py-2 text-right"></td>
                                        <td class="px-3 py-2 text-right"></td>
                                        <td class="px-3 py-2 text-right"></td>
                                        <td class="px-3 py-2 font-medium text-right tabular-nums">
                                            {{ money($data['exchange_rate'] * $accountData['opening_balance'], $data['currency_code'], true) }}
                                        </td>
                                    </tr>
                                @endif

                                {{-- Transaction Rows --}}
                                @foreach($accountData['transactions'] as $transaction)
                                    <tr class="border-b hover:bg-gray-50">
                                        <td class="px-3 py-2 whitespace-nowrap">{{ \Carbon\Carbon::parse($transaction['date'])->format('d M Y') }}</td>
                                        <td class="px-3 py-2">{{ $transaction['details'] }}</td>
                                        <td class="px-3 py-2">{{ $transaction['ref_type'] }}</td>
                                        <td class="px-3 py-2 text-right">{{ $transaction['ref_number'] }}</td>
                                        <td class="px-3 py-2 text-right tabular-nums">
                                            @if($transaction['debit'] > 0)
                                                {{ number_format($transaction['debit'] * $data['exchange_rate'], 2) }}
                                            @endif
                                        </td>
                                        <td class="px-3 py-2 text-right tabular-nums">
                                            @if($transaction['credit'] > 0)
                                                {{ number_format($transaction['credit'] * $data['exchange_rate'], 2) }}
                                            @endif
                                        </td>
                                        <td class="px-3 py-2 font-medium text-right tabular-nums">
                                            {{ money($data['exchange_rate'] * $transaction['balance'], $data['currency_code'], true) }}
                                        </td>
                                    </tr>
                                @endforeach

                                {{-- Closing Balance Row --}}
                                <tr class="font-medium border-t-2 bg-green-50">
                                    <td class="px-3 py-2 text-gray-600">{{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}</td>
                                    <td class="px-3 py-2 font-semibold text-green-700">Closing Balance</td>
                                    <td class="px-3 py-2"></td>
                                    <td class="px-3 py-2 text-right"></td>
                                    <td class="px-3 py-2 text-right"></td>
                                    <td class="px-3 py-2 text-right"></td>
                                    <td class="px-3 py-2 font-semibold text-right tabular-nums">
                                        {{ money($data['exchange_rate'] * $accountData['closing_balance'], $data['currency_code'], true) }}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="py-8 text-center text-gray-500">
                        <p>No transactions found for this account in the selected period.</p>
                        @if($accountData['opening_balance'] != 0)
                            <p class="mt-2">
                                Account balance remains:
                                <span class="font-medium tabular-nums">
                                    {{ money($data['exchange_rate'] * $accountData['opening_balance'], $data['currency_code'], true) }}
                                </span>
                            </p>
                        @endif
                    </div>
                @endif

                {{-- Account Summary --}}
                @if(count($accountData['transactions']) > 0)
                    @php
                        $totalDebits = collect($accountData['transactions'])->sum('debit');
                        $totalCredits = collect($accountData['transactions'])->sum('credit');
                    @endphp
                    <div class="grid grid-cols-3 gap-4 p-3 pt-4 border-t rounded bg-gray-50">
                        <div class="text-center">
                            <div class="text-xs text-gray-600">Total Debits</div>
                            <div class="font-medium tabular-nums">{{ money($data['exchange_rate'] * $totalDebits, $data['currency_code'], true) }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xs text-gray-600">Total Credits</div>
                            <div class="font-medium tabular-nums">{{ money($data['exchange_rate'] * $totalCredits, $data['currency_code'], true) }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xs text-gray-600">Net Change</div>
                            <div class="font-medium tabular-nums">{{ money($data['exchange_rate'] * ($accountData['closing_balance'] - $accountData['opening_balance']), $data['currency_code'], true) }}</div>
                        </div>
                    </div>
                @endif
            </div>
        @endforeach
    @else
        <div class="py-12 text-center text-gray-500">
            <div class="text-lg font-medium">No transactions found</div>
            <p class="mt-2">No journal entries were found for the selected period and criteria.</p>
        </div>
    @endif
</div>
