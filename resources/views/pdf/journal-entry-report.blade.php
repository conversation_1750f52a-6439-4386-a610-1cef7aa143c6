<x-layouts.document>
    <x-slot name="title">
        Journal Entry Report
    </x-slot>

    <x-slot name="header">
        <div class="text-center">
            <h1 class="text-2xl font-bold">Journal Entry Report</h1>
            <p class="text-sm text-gray-600">
                Period: {{ \Carbon\Carbon::parse($period['start'])->format('d M Y') }} - {{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}
            </p>
            @if($data['selected_account_id'])
                <p class="text-xs text-gray-500">Filtered for specific account</p>
            @endif
        </div>
    </x-slot>

    <x-finance.journal-entry-report :data="$data" :period="$period" />
</x-layouts.document>
