<x-layouts.document
    title="Advance Cash Report"
    size="A4"
    orientation="portrait"
    margin="1.5cm"
>
    <style>
        html, body {
            font-size: 9pt;
            line-height: 1.3;
        }
        .user-header {
            background-color: #f3f4f6;
            padding: 8px;
            margin-top: 20px;
            border: 1px solid #d1d5db;
        }
        .transaction-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .transaction-table th,
        .transaction-table td {
            border: 1px solid #d1d5db;
            padding: 4px 6px;
            text-align: left;
        }
        .transaction-table th {
            background-color: #f9fafb;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .opening-balance {
            background-color: #dbeafe;
        }
        .closing-balance {
            background-color: #dcfce7;
            font-weight: bold;
        }
        .summary-box {
            background-color: #f9fafb;
            border: 1px solid #d1d5db;
            padding: 8px;
            margin-top: 10px;
        }
        .page-break {
            page-break-before: always;
        }
        .positive-amount {
            color: #059669;
        }
        .negative-amount {
            color: #dc2626;
        }
        .advance-amount {
            color: #059669;
            font-weight: bold;
        }
        .return-amount {
            color: #dc2626;
            font-weight: bold;
        }
        .overall-summary {
            background-color: #eff6ff;
            border: 2px solid #3b82f6;
            padding: 12px;
            margin-top: 20px;
        }
    </style>

    <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="margin: 0; font-size: 18pt; font-weight: bold;">Advance Cash Report</h1>
        <p style="margin: 5px 0; font-size: 11pt;">
            Period: {{ \Carbon\Carbon::parse($period['start'])->format('d M Y') }} - {{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}
        </p>
        @if($data['selected_user_id'])
            <p style="margin: 5px 0; font-size: 9pt; color: #6b7280;">Filtered for specific user</p>
        @endif
    </div>

    @if(count($data['users']) > 0)
        @foreach($data['users'] as $index => $userData)
            @if($index > 0)
                <div class="page-break"></div>
            @endif

            <div class="user-header">
                <h2 style="margin: 0; font-size: 12pt; font-weight: bold;">{{ $userData['user']['name'] }}</h2>
                @if($userData['opening_balance'] != 0)
                    <p style="margin: 5px 0; font-size: 9pt;">
                        Opening Balance:
                        <span class="{{ $userData['opening_balance'] >= 0 ? 'positive-amount' : 'negative-amount' }}">
                            {{ money($data['exchange_rate'] * $userData['opening_balance'], $data['currency_code'], true) }}
                        </span>
                    </p>
                @endif
            </div>

            @if(count($userData['transactions']) > 0)
                <table class="transaction-table">
                    <thead>
                        <tr>
                            <th style="width: 12%;">Date</th>
                            <th style="width: 35%;">Description</th>
                            <th style="width: 10%;">Ref #</th>
                            <th style="width: 14%; text-align: right;">Advance</th>
                            <th style="width: 14%; text-align: right;">Return/Expense</th>
                            <th style="width: 15%; text-align: right;">Balance</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{-- Opening Balance Row --}}
                        @if($userData['opening_balance'] != 0)
                            <tr class="opening-balance">
                                <td>{{ \Carbon\Carbon::parse($period['start'])->subDay()->format('d M Y') }}</td>
                                <td><strong>Opening Balance</strong></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td class="text-right {{ $userData['opening_balance'] >= 0 ? 'positive-amount' : 'negative-amount' }}">
                                    <strong>{{ money($data['exchange_rate'] * $userData['opening_balance'], $data['currency_code'], true) }}</strong>
                                </td>
                            </tr>
                        @endif

                        {{-- Transaction Rows --}}
                        @foreach($userData['transactions'] as $transaction)
                            <tr>
                                <td>{{ \Carbon\Carbon::parse($transaction['date'])->format('d M Y') }}</td>
                                <td>{{ $transaction['details'] }}</td>
                                <td style="font-size: 8pt;">{{ $transaction['ref_number'] }}</td>
                                <td class="text-right">
                                    @if($transaction['debit'] > 0)
                                        <span class="advance-amount">
                                            {{ number_format($transaction['debit'] * $data['exchange_rate'], 2) }}
                                        </span>
                                    @endif
                                </td>
                                <td class="text-right">
                                    @if($transaction['credit'] > 0)
                                        <span class="return-amount">
                                            {{ number_format($transaction['credit'] * $data['exchange_rate'], 2) }}
                                        </span>
                                    @endif
                                </td>
                                <td class="text-right {{ $transaction['balance'] >= 0 ? 'positive-amount' : 'negative-amount' }}">
                                    <strong>{{ money($data['exchange_rate'] * $transaction['balance'], $data['currency_code'], true) }}</strong>
                                </td>
                            </tr>
                        @endforeach

                        {{-- Closing Balance Row --}}
                        <tr class="closing-balance">
                            <td>{{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}</td>
                            <td><strong>Closing Balance</strong></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td class="text-right {{ $userData['closing_balance'] >= 0 ? 'positive-amount' : 'negative-amount' }}">
                                <strong>{{ money($data['exchange_rate'] * $userData['closing_balance'], $data['currency_code'], true) }}</strong>
                            </td>
                        </tr>
                    </tbody>
                </table>

                {{-- User Summary --}}
                @php
                    $totalAdvances = collect($userData['transactions'])->sum('debit');
                    $totalReturns = collect($userData['transactions'])->sum('credit');
                    $netChange = $userData['closing_balance'] - $userData['opening_balance'];
                @endphp
                <div class="summary-box">
                    <table style="width: 100%; border: none;">
                        <tr>
                            <td style="border: none; width: 25%; text-align: center;">
                                <div style="font-size: 8pt; color: #6b7280;">Total Advances</div>
                                <div class="advance-amount">{{ money($data['exchange_rate'] * $totalAdvances, $data['currency_code'], true) }}</div>
                            </td>
                            <td style="border: none; width: 25%; text-align: center;">
                                <div style="font-size: 8pt; color: #6b7280;">Total Returns/Expenses</div>
                                <div class="return-amount">{{ money($data['exchange_rate'] * $totalReturns, $data['currency_code'], true) }}</div>
                            </td>
                            <td style="border: none; width: 25%; text-align: center;">
                                <div style="font-size: 8pt; color: #6b7280;">Net Change</div>
                                <div class="{{ $netChange >= 0 ? 'positive-amount' : 'negative-amount' }}">
                                    <strong>{{ money($data['exchange_rate'] * $netChange, $data['currency_code'], true) }}</strong>
                                </div>
                            </td>
                            <td style="border: none; width: 25%; text-align: center;">
                                <div style="font-size: 8pt; color: #6b7280;">Outstanding Balance</div>
                                <div class="{{ $userData['closing_balance'] >= 0 ? 'positive-amount' : 'negative-amount' }}">
                                    <strong>{{ money($data['exchange_rate'] * $userData['closing_balance'], $data['currency_code'], true) }}</strong>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            @else
                <div style="text-align: center; padding: 20px; color: #6b7280;">
                    <p>No transactions found for this user in the selected period.</p>
                    @if($userData['opening_balance'] != 0)
                        <p>
                            User balance remains:
                            <span class="{{ $userData['opening_balance'] >= 0 ? 'positive-amount' : 'negative-amount' }}">
                                <strong>{{ money($data['exchange_rate'] * $userData['opening_balance'], $data['currency_code'], true) }}</strong>
                            </span>
                        </p>
                    @endif
                </div>
            @endif
        @endforeach

        {{-- Overall Summary --}}
        @if(count($data['users']) > 1)
            @php
                $totalOpeningBalance = collect($data['users'])->sum('opening_balance');
                $totalClosingBalance = collect($data['users'])->sum('closing_balance');
                $totalAdvances = collect($data['users'])->sum(function($user) {
                    return collect($user['transactions'])->sum('debit');
                });
                $totalReturns = collect($data['users'])->sum(function($user) {
                    return collect($user['transactions'])->sum('credit');
                });
            @endphp
            <div class="overall-summary">
                <h3 style="margin: 0 0 10px 0; font-size: 12pt; color: #1e40af;">Overall Summary</h3>
                <table style="width: 100%; border: none;">
                    <tr>
                        <td style="border: none; width: 25%; text-align: center;">
                            <div style="font-size: 8pt; color: #3b82f6;">Total Opening Balance</div>
                            <div class="{{ $totalOpeningBalance >= 0 ? 'positive-amount' : 'negative-amount' }}">
                                <strong>{{ money($data['exchange_rate'] * $totalOpeningBalance, $data['currency_code'], true) }}</strong>
                            </div>
                        </td>
                        <td style="border: none; width: 25%; text-align: center;">
                            <div style="font-size: 8pt; color: #3b82f6;">Total Advances</div>
                            <div class="advance-amount">
                                <strong>{{ money($data['exchange_rate'] * $totalAdvances, $data['currency_code'], true) }}</strong>
                            </div>
                        </td>
                        <td style="border: none; width: 25%; text-align: center;">
                            <div style="font-size: 8pt; color: #3b82f6;">Total Returns/Expenses</div>
                            <div class="return-amount">
                                <strong>{{ money($data['exchange_rate'] * $totalReturns, $data['currency_code'], true) }}</strong>
                            </div>
                        </td>
                        <td style="border: none; width: 25%; text-align: center;">
                            <div style="font-size: 8pt; color: #3b82f6;">Total Outstanding</div>
                            <div class="{{ $totalClosingBalance >= 0 ? 'positive-amount' : 'negative-amount' }}">
                                <strong>{{ money($data['exchange_rate'] * $totalClosingBalance, $data['currency_code'], true) }}</strong>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
        @endif
    @else
        <div style="text-align: center; padding: 40px; color: #6b7280;">
            <h2 style="font-size: 14pt;">No advance cash transactions found</h2>
            <p>No user cash transactions were found for the selected period and criteria.</p>
        </div>
    @endif
</x-layouts.document>
