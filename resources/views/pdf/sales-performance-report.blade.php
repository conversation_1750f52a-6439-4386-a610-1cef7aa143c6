<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Sales Performance Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10pt;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 18pt;
            color: #1a2b48;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .summary-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .summary-row {
            display: table-row;
        }
        .summary-cell {
            display: table-cell;
            width: 25%;
            padding: 10px;
            border: 1px solid #ddd;
            text-align: center;
            vertical-align: middle;
        }
        .summary-cell .label {
            font-size: 8pt;
            color: #666;
            margin-bottom: 5px;
        }
        .summary-cell .value {
            font-size: 12pt;
            font-weight: bold;
            color: #1a2b48;
        }
        .section {
            margin-bottom: 25px;
        }
        .section h2 {
            font-size: 14pt;
            color: #1a2b48;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        table th {
            background-color: #1a2b48;
            color: white;
            padding: 8px;
            text-align: left;
            font-size: 9pt;
        }
        table td {
            padding: 6px 8px;
            border-bottom: 1px solid #ddd;
            font-size: 9pt;
        }
        table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .page-break {
            page-break-before: always;
        }
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 30px;
            text-align: center;
            font-size: 8pt;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 5px;
        }
    </style>
</head>
<body>
    <div class="footer">
        Sales Performance Report - Generated on {{ now()->format('d M Y H:i') }}
    </div>

    <div class="header">
        <h1>Sales Performance Report</h1>
        <p>Period: {{ \Carbon\Carbon::parse($period['start'])->format('d M Y') }} - {{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}</p>
        @if(isset($data['customer_filter']) && $data['customer_filter'])
            <p>Customer: {{ $data['customer_filter'] }}</p>
        @endif
    </div>

    {{-- Summary Metrics --}}
    <div class="section">
        <h2>Summary Metrics</h2>
        <div class="summary-grid">
            <div class="summary-row">
                <div class="summary-cell">
                    <div class="label">Total Sales</div>
                    <div class="value">{{ money($data['summary']['total_sales'], $data['currency_code'] ?? 'SAR', true) }}</div>
                </div>
                <div class="summary-cell">
                    <div class="label">Total Paid</div>
                    <div class="value">{{ money($data['summary']['total_paid'], $data['currency_code'] ?? 'SAR', true) }}</div>
                </div>
                <div class="summary-cell">
                    <div class="label">Outstanding</div>
                    <div class="value">{{ money($data['summary']['total_outstanding'], $data['currency_code'] ?? 'SAR', true) }}</div>
                </div>
                <div class="summary-cell">
                    <div class="label">Payment Rate</div>
                    <div class="value">{{ number_format($data['summary']['payment_rate'], 1) }}%</div>
                </div>
            </div>
            <div class="summary-row">
                <div class="summary-cell">
                    <div class="label">Total Invoices</div>
                    <div class="value">{{ number_format($data['summary']['total_invoices']) }}</div>
                </div>
                <div class="summary-cell">
                    <div class="label">Avg Invoice Value</div>
                    <div class="value">{{ money($data['summary']['average_invoice_value'], $data['currency_code'] ?? 'SAR', true) }}</div>
                </div>
                <div class="summary-cell">
                    <div class="label">Unique Customers</div>
                    <div class="value">{{ number_format($data['summary']['unique_customers']) }}</div>
                </div>
                <div class="summary-cell">
                    <div class="label">Unique Groups</div>
                    <div class="value">{{ number_format($data['summary']['unique_groups']) }}</div>
                </div>
            </div>
        </div>
    </div>

    {{-- Sales Data by Group --}}
    <div class="section">
        <h2>Sales Data by {{ ucfirst($data['group_by']) }}</h2>
        <table>
            <thead>
                <tr>
                    <th>{{ ucfirst($data['group_by']) }}</th>
                    <th class="text-center">Invoices</th>
                    <th class="text-right">Sales</th>
                    <th class="text-right">Paid</th>
                    <th class="text-right">Outstanding</th>
                    <th class="text-center">Payment Rate</th>
                </tr>
            </thead>
            <tbody>
                @foreach($data['grouped_data'] as $row)
                    <tr>
                        <td>{{ $row['label'] }}</td>
                        <td class="text-center">{{ number_format($row['count']) }}</td>
                        <td class="text-right">{{ money($row['sales'], $data['currency_code'] ?? 'SAR', true) }}</td>
                        <td class="text-right">{{ money($row['paid'], $data['currency_code'] ?? 'SAR', true) }}</td>
                        <td class="text-right">{{ money($row['outstanding'], $data['currency_code'] ?? 'SAR', true) }}</td>
                        <td class="text-center">{{ $row['sales'] > 0 ? number_format(($row['paid'] / $row['sales']) * 100, 1) : 0 }}%</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    {{-- Top Customers --}}
    <div class="section">
        <h2>Top 10 Customers by Sales</h2>
        <table>
            <thead>
                <tr>
                    <th>Customer</th>
                    <th class="text-right">Sales</th>
                    <th class="text-center">Invoices</th>
                    <th class="text-center">Payment Rate</th>
                </tr>
            </thead>
            <tbody>
                @foreach($data['top_customers'] as $customer)
                    <tr>
                        <td>{{ $customer['name'] }}</td>
                        <td class="text-right">{{ money($customer['sales'], $data['currency_code'] ?? 'SAR', true) }}</td>
                        <td class="text-center">{{ number_format($customer['invoices']) }}</td>
                        <td class="text-center">{{ number_format($customer['payment_rate'], 1) }}%</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    {{-- Invoice Status Breakdown --}}
    <div class="section">
        <h2>Invoice Status Breakdown</h2>
        <table>
            <thead>
                <tr>
                    <th>Status</th>
                    <th class="text-center">Count</th>
                    <th class="text-center">Percentage</th>
                    <th class="text-right">Sales</th>
                </tr>
            </thead>
            <tbody>
                @foreach($data['status_breakdown'] as $status)
                    <tr>
                        <td>{{ $status['status'] }}</td>
                        <td class="text-center">{{ number_format($status['count']) }}</td>
                        <td class="text-center">{{ number_format($status['percentage'], 1) }}%</td>
                        <td class="text-right">{{ money($status['sales'], $data['currency_code'] ?? 'SAR', true) }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    {{-- Payment Trends --}}
    @if(count($data['payment_trends']) > 0)
        <div class="section">
            <h2>Payment Trends</h2>
            <table>
                <thead>
                    <tr>
                        <th>Month</th>
                        <th class="text-right">Payment Amount</th>
                        <th class="text-center">Payment Count</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['payment_trends'] as $trend)
                        <tr>
                            <td>{{ $trend['month'] }}</td>
                            <td class="text-right">{{ money($trend['amount'], $data['currency_code'] ?? 'SAR', true) }}</td>
                            <td class="text-center">{{ number_format($trend['count']) }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @endif
</body>
</html>
