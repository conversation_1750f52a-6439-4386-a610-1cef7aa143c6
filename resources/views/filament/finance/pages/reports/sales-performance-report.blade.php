<x-filament-panels::page>
    <x-filament::section>
        <x-filament-panels::form wire:submit="filter">
            {{ $this->form }}

            <div class="flex items-center justify-end">
                <x-filament-panels::form.actions :actions="$this->getCachedFormActions()" :full-width="$this->hasFullWidthFormActions()" />
            </div>
        </x-filament-panels::form>
    </x-filament::section>

    @if ($data !== null)
        <x-filament::section>
            <x-finance.sales-performance-report :data="$data" :period="$period" />
        </x-filament::section>
    @endif

    <script>
        document.addEventListener('livewire:initialized', () => {
            @this.on('download-file', (url) => {
                const link = document.createElement('a');
                link.href = url;
                link.download = '';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            });
        });
    </script>
</x-filament-panels::page>
