<x-filament-panels::page>
    @if($record->attachments && count($record->attachments) > 0)
    <div class="mb-6">
        <x-filament::section>
            <x-slot name="heading">
                Attachments
            </x-slot>

            <div class="grid grid-cols-1 gap-4">
                @foreach($record->attachments as $attachment)
                    <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                        <x-heroicon-o-document class="w-5 h-5 text-gray-400" />
                        <div class="flex-1">
                            <a href="{{ \Illuminate\Support\Facades\Storage::disk('s3')->url($attachment) }}"
                               target="_blank"
                               class="text-primary-600 hover:text-primary-500 font-medium">
                                {{ basename($attachment) }}
                            </a>
                        </div>
                        <a href="{{ \Illuminate\Support\Facades\Storage::disk('s3')->url($attachment) }}"
                           download
                           class="text-gray-400 hover:text-gray-600">
                            <x-heroicon-o-arrow-down-tray class="w-4 h-4" />
                        </a>
                    </div>
                @endforeach
            </div>
        </x-filament::section>
    </div>
    @endif

    <div class="overflow-x-auto">
        <div class="mx-auto bg-white rounded-xl shadow" style="width: 21cm; padding: 1cm; font-size: 10pt;" id="bill-print">
            <x-finance.bill :record="$record" is-inline />
        </div>
    </div>
</x-filament-panels::page>
