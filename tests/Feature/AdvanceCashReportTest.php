<?php

use App\Enums\Finance\AccountCategory;
use App\Filament\Finance\Pages\Reports\AdvanceCashReport;
use App\Models\Currency;
use App\Models\Finance\CashAccount;
use App\Models\Finance\UserCash;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create a user and authenticate
    $user = User::factory()->create();
    $this->actingAs($user);

    // Create currencies (use firstOrCreate to avoid duplicates)
    Currency::firstOrCreate(['code' => 'SAR'], ['name' => 'Saudi Riyal', 'exchange_rate' => 1]);
    Currency::firstOrCreate(['code' => 'IDR'], ['name' => 'Indonesian Rupiah', 'exchange_rate' => 3700]);

    // Create required cash accounts for journal entries
    CashAccount::create([
        'code' => config('finance.coa.advance_cash'),
        'category' => AccountCategory::CashBank,
        'name' => 'Advance Cash',
        'is_fixed' => true,
    ]);
    CashAccount::factory()->mainCash()->create();
    CashAccount::create([
        'code' => config('finance.coa.default_expense'),
        'category' => AccountCategory::OperatingExpense,
        'name' => 'Default Expense',
        'is_fixed' => true,
    ]);
});

it('can render advance cash report page', function () {
    Livewire::test(AdvanceCashReport::class)
        ->assertSuccessful();
});

it('includes users with non-zero balances even when they have no transactions in selected period', function () {
    // Create test users
    $userWithBalance = User::factory()->create(['name' => 'User With Balance']);
    $userWithTransactions = User::factory()->create(['name' => 'User With Transactions']);
    $userWithZeroBalance = User::factory()->create(['name' => 'User With Zero Balance']);

    // Create transactions before the report period for userWithBalance
    UserCash::create([
        'user_id' => $userWithBalance->id,
        'cashed_at' => Carbon::now()->subDays(30), // Before report period
        'type' => 'd', // Debit (advance given)
        'amount' => 1000,
        'currency' => 'SAR',
        'exchange_rate' => 1,
        'details' => 'Initial advance',
    ]);

    // Create transactions that result in zero balance for userWithZeroBalance
    UserCash::create([
        'user_id' => $userWithZeroBalance->id,
        'cashed_at' => Carbon::now()->subDays(30),
        'type' => 'd',
        'amount' => 500,
        'currency' => 'SAR',
        'exchange_rate' => 1,
        'details' => 'Advance given',
    ]);
    UserCash::create([
        'user_id' => $userWithZeroBalance->id,
        'cashed_at' => Carbon::now()->subDays(25),
        'type' => 'c',
        'amount' => 500,
        'currency' => 'SAR',
        'exchange_rate' => 1,
        'details' => 'Advance returned',
    ]);

    // Create transactions within the report period for userWithTransactions
    UserCash::create([
        'user_id' => $userWithTransactions->id,
        'cashed_at' => Carbon::now()->subDays(5), // Within report period
        'type' => 'd',
        'amount' => 750,
        'currency' => 'SAR',
        'exchange_rate' => 1,
        'details' => 'Recent advance',
    ]);

    // Generate report for the last 10 days
    $startDate = Carbon::now()->subDays(10)->format('d/m/Y');
    $endDate = Carbon::now()->format('d/m/Y');

    $component = Livewire::test(AdvanceCashReport::class)
        ->fillForm([
            'date_range' => $startDate . ' - ' . $endDate,
            'currency_code' => 'SAR',
        ])
        ->call('filter');

    $data = $component->get('data');

    // Verify that the report includes the correct users
    expect($data)->not->toBeNull();
    expect($data['users'])->toHaveCount(2); // userWithBalance and userWithTransactions

    // Find users in the data
    $usersById = collect($data['users'])->keyBy('user.id');

    // userWithBalance should be included (has non-zero balance, no transactions in period)
    expect($usersById->has($userWithBalance->id))->toBeTrue();
    $userWithBalanceData = $usersById[$userWithBalance->id];
    expect($userWithBalanceData['opening_balance'])->toBe(1000.0);
    expect($userWithBalanceData['closing_balance'])->toBe(1000.0);
    expect($userWithBalanceData['transactions'])->toBeEmpty();

    // userWithTransactions should be included (has transactions in period)
    expect($usersById->has($userWithTransactions->id))->toBeTrue();
    $userWithTransactionsData = $usersById[$userWithTransactions->id];
    expect($userWithTransactionsData['transactions'])->toHaveCount(1);

    // userWithZeroBalance should NOT be included (has zero balance)
    expect($usersById->has($userWithZeroBalance->id))->toBeFalse();
});

it('respects user filter when including users with non-zero balances', function () {
    // Create test users
    $selectedUser = User::factory()->create(['name' => 'Selected User']);
    $otherUser = User::factory()->create(['name' => 'Other User']);

    // Both users have non-zero balances from transactions before the report period
    UserCash::create([
        'user_id' => $selectedUser->id,
        'cashed_at' => Carbon::now()->subDays(30),
        'type' => 'd',
        'amount' => 1000,
        'currency' => 'SAR',
        'exchange_rate' => 1,
        'details' => 'Advance for selected user',
    ]);

    UserCash::create([
        'user_id' => $otherUser->id,
        'cashed_at' => Carbon::now()->subDays(30),
        'type' => 'd',
        'amount' => 500,
        'currency' => 'SAR',
        'exchange_rate' => 1,
        'details' => 'Advance for other user',
    ]);

    // Generate report filtered for selectedUser only
    $startDate = Carbon::now()->subDays(10)->format('d/m/Y');
    $endDate = Carbon::now()->format('d/m/Y');

    $component = Livewire::test(AdvanceCashReport::class)
        ->fillForm([
            'date_range' => $startDate . ' - ' . $endDate,
            'currency_code' => 'SAR',
            'user_id' => $selectedUser->id,
        ])
        ->call('filter');

    $data = $component->get('data');

    // Verify that only the selected user is included
    expect($data)->not->toBeNull();
    expect($data['users'])->toHaveCount(1);
    expect($data['users'][0]['user']['id'])->toBe($selectedUser->id);
    expect($data['users'][0]['opening_balance'])->toBe(1000.0);
});

it('calculates balances correctly for users with no transactions in period', function () {
    $user = User::factory()->create(['name' => 'Test User']);

    // Create multiple transactions before the report period
    UserCash::create([
        'user_id' => $user->id,
        'cashed_at' => Carbon::now()->subDays(30),
        'type' => 'd',
        'amount' => 1500,
        'currency' => 'SAR',
        'exchange_rate' => 1,
        'details' => 'Initial advance',
    ]);

    UserCash::create([
        'user_id' => $user->id,
        'cashed_at' => Carbon::now()->subDays(25),
        'type' => 'c',
        'amount' => 300,
        'currency' => 'SAR',
        'exchange_rate' => 1,
        'details' => 'Partial return',
    ]);

    UserCash::create([
        'user_id' => $user->id,
        'cashed_at' => Carbon::now()->subDays(20),
        'type' => 'd',
        'amount' => 200,
        'currency' => 'SAR',
        'exchange_rate' => 1,
        'details' => 'Additional advance',
    ]);

    // Generate report for the last 10 days (no transactions in this period)
    $startDate = Carbon::now()->subDays(10)->format('d/m/Y');
    $endDate = Carbon::now()->format('d/m/Y');

    $component = Livewire::test(AdvanceCashReport::class)
        ->fillForm([
            'date_range' => $startDate . ' - ' . $endDate,
            'currency_code' => 'SAR',
        ])
        ->call('filter');

    $data = $component->get('data');

    // Verify the user is included with correct balance calculation
    expect($data)->not->toBeNull();
    expect($data['users'])->toHaveCount(1);

    $userData = $data['users'][0];
    expect($userData['user']['id'])->toBe($user->id);

    // Expected balance: 1500 (debit) - 300 (credit) + 200 (debit) = 1400
    expect($userData['opening_balance'])->toBe(1400.0);
    expect($userData['closing_balance'])->toBe(1400.0);
    expect($userData['transactions'])->toBeEmpty();
});
