<?php

use App\Filament\Finance\Pages\Reports\SalesPerformanceReport;
use App\Models\Currency;
use App\Models\Customer;
use App\Models\Finance\Invoice;
use App\Models\User;
use Livewire\Livewire;

beforeEach(function () {
    // Create a user and authenticate
    $user = User::factory()->create();
    $this->actingAs($user);

    // Create currencies (SAR is base currency with rate 1, IDR has higher rate)
    Currency::create(['code' => 'SAR', 'name' => 'Saudi Riyal', 'exchange_rate' => 1]);
    Currency::create(['code' => 'IDR', 'name' => 'Indonesian Rupiah', 'exchange_rate' => 3700]);
});

it('can render sales performance report page', function () {
    Livewire::test(SalesPerformanceReport::class)
        ->assertSuccessful();
});

it('has currency selector in form', function () {
    Livewire::test(SalesPerformanceReport::class)
        ->assertFormFieldExists('filters.currency_code');
});

it('can generate report with different currencies', function () {
    // Create test data
    $customer = Customer::factory()->create();
    $invoice = Invoice::factory()->create([
        'customer_id' => $customer->id,
        'total' => 1000,
        'paid' => 500,
        'invoice_date' => now(),
    ]);

    // Test with SAR currency (base currency)
    Livewire::test(SalesPerformanceReport::class)
        ->fillForm([
            'date_range' => now()->startOfYear()->format('d/m/Y') . ' - ' . now()->format('d/m/Y'),
            'currency_code' => 'SAR',
            'group_by' => 'month',
        ])
        ->call('filter')
        ->assertSet('data.currency_code', 'SAR')
        ->assertSet('data.exchange_rate', 1);

    // Test with IDR currency
    Livewire::test(SalesPerformanceReport::class)
        ->fillForm([
            'date_range' => now()->startOfYear()->format('d/m/Y') . ' - ' . now()->format('d/m/Y'),
            'currency_code' => 'IDR',
            'group_by' => 'month',
        ])
        ->call('filter')
        ->assertSet('data.currency_code', 'IDR')
        ->assertSet('data.exchange_rate', 3700);
});

it('applies currency conversion to summary metrics', function () {
    // Create test data
    $customer = Customer::factory()->create();
    $invoice = Invoice::factory()->create([
        'customer_id' => $customer->id,
        'total' => 1000, // 1000 SAR (base currency)
        'paid' => 500,   // 500 SAR (base currency)
        'invoice_date' => now(),
    ]);

    // Test with SAR currency (base currency, no conversion)
    $component = Livewire::test(SalesPerformanceReport::class)
        ->fillForm([
            'date_range' => now()->startOfYear()->format('d/m/Y') . ' - ' . now()->format('d/m/Y'),
            'currency_code' => 'SAR',
            'group_by' => 'month',
        ])
        ->call('filter');

    $data = $component->get('data');

    // Check that no conversion is applied for base currency
    expect($data['currency_code'])->toBe('SAR');
    expect($data['exchange_rate'])->toBe(1.0);
    expect($data['summary']['total_sales'])->toBe(1000.0);
    expect($data['summary']['total_paid'])->toBe(500.0);

    // Test with IDR currency (exchange rate 3700)
    $component = Livewire::test(SalesPerformanceReport::class)
        ->fillForm([
            'date_range' => now()->startOfYear()->format('d/m/Y') . ' - ' . now()->format('d/m/Y'),
            'currency_code' => 'IDR',
            'group_by' => 'month',
        ])
        ->call('filter');

    $data = $component->get('data');

    // Check that currency conversion is applied
    expect($data['currency_code'])->toBe('IDR');
    expect($data['exchange_rate'])->toBe(3700.0);

    // Check that amounts are converted (1000 SAR * 3700 = 3,700,000 IDR)
    expect($data['summary']['total_sales'])->toBe(3700000.0);
    expect($data['summary']['total_paid'])->toBe(1850000.0);
});
