<?php

namespace App\Exports;

use App\Enums\Finance\AccountCategory;
use App\Models\Finance\CashAccount;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ChartOfAccountsExport implements FromQuery, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithStyles
{
    protected $startDate;

    protected $endDate;

    public function __construct($startDate = null, $endDate = null)
    {
        $this->startDate = $startDate ?? today()->startOfYear();
        $this->endDate = $endDate ?? today();
    }

    public function query()
    {
        return CashAccount::query()
            ->select([
                'cash_accounts.id',
                'cash_accounts.code',
                'cash_accounts.name',
                'cash_accounts.description',
                'cash_accounts.category',
                'cash_accounts.is_fixed',
                DB::raw("
                    COALESCE(
                        SUM(IF(journal_entry_items.type = 'd' AND journal_entries.entry_date < ?, journal_entry_items.amount, 0)) -
                        SUM(IF(journal_entry_items.type = 'c' AND journal_entries.entry_date < ?, journal_entry_items.amount, 0))
                    , 0) AS starting_balance
                "),
                DB::raw("
                    COALESCE(
                        SUM(IF(journal_entry_items.type = 'd' AND journal_entries.entry_date BETWEEN ? AND ?, journal_entry_items.amount, 0)) -
                        SUM(IF(journal_entry_items.type = 'c' AND journal_entries.entry_date BETWEEN ? AND ?, journal_entry_items.amount, 0))
                    , 0) AS net_movement
                "),
                DB::raw('COUNT(journal_entry_items.id) AS journal_entry_items_count'),
            ])
            ->leftJoin('journal_entry_items', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
            ->leftJoin('journal_entries', function (JoinClause $join) {
                $join->on('journal_entry_items.entry_id', '=', 'journal_entries.id')
                    ->where('journal_entries.entry_date', '<=', $this->endDate);
            })
            ->groupBy([
                'cash_accounts.id',
                'cash_accounts.code',
                'cash_accounts.name',
                'cash_accounts.description',
                'cash_accounts.category',
                'cash_accounts.is_fixed',
            ])
            ->withCasts([
                'starting_balance' => 'float',
                'net_movement' => 'float',
            ])
            ->addBinding([
                $this->startDate,
                $this->startDate,
                $this->startDate,
                $this->endDate,
                $this->startDate,
                $this->endDate,
            ], 'select')
            ->orderByRaw("FIELD(`category`, '" . collect(AccountCategory::orderedCases())->map(fn ($c) => $c->value)->join("','") . "')")
            ->orderBy('code');
    }

    public function headings(): array
    {
        return [
            'Account Code',
            'Account Name',
            'Description',
            'Category',
            'Starting Balance',
            'Net Movement',
            'Current Balance',
            // 'Transaction Count',
            // 'Is Fixed',
        ];
    }

    public function map($account): array
    {
        // Calculate the current balance based on account category
        $currentBalance = $account->category->isReal()
            ? ($account->starting_balance + $account->net_movement) * ($account->category->isNormalDebitBalance() ? 1 : -1)
            : $account->net_movement * ($account->category->isNormalDebitBalance() ? 1 : -1);

        return [
            $account->code,
            $account->name,
            $account->description,
            $account->category->getLabel(),
            $account->category->isReal()
                ? $account->starting_balance * ($account->category->isNormalDebitBalance() ? 1 : -1)
                : 0,
            $account->net_movement * ($account->category->isNormalDebitBalance() ? 1 : -1),
            $currentBalance,
            // $account->journal_entry_items_count,
            // $account->is_fixed ? 'Yes' : 'No',
        ];
    }

    public function columnFormats(): array
    {
        return [
            'E' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1, // Starting Balance
            'F' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1, // Net Movement
            'G' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1, // Current Balance
            // 'H' => NumberFormat::FORMAT_NUMBER,                  // Transaction Count
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the header row
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => 'FFE2E8F0',
                    ],
                ],
            ],
        ];
    }
}
