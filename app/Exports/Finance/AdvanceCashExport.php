<?php

namespace App\Exports\Finance;

use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class AdvanceCashExport implements WithMultipleSheets
{
    use Exportable;

    private $data;

    private $period;

    private $fileName;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
        $this->fileName = 'advance-cash-report-' . $period['start'] . '-to-' . $period['end'] . '.xlsx';
    }

    public function sheets(): array
    {
        $sheets = [];

        // Summary sheet
        $sheets[] = new AdvanceCashSummarySheet($this->data, $this->period);

        // Individual user sheets
        $userCount = 0;
        foreach ($this->data['users'] as $userData) {
            if (count($userData['transactions']) > 0) {
                $sheets[] = new AdvanceCashUserSheet($userData, $this->period, $this->data['exchange_rate'], $this->data['currency_code']);
                $userCount++;
            }
        }

        return $sheets;
    }
}

class AdvanceCashSummarySheet implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    private $data;

    private $period;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
    }

    public function collection()
    {
        $collection = collect();

        foreach ($this->data['users'] as $userData) {
            $totalDebits = collect($userData['transactions'])->sum('debit');
            $totalCredits = collect($userData['transactions'])->sum('credit');

            $collection->push((object) [
                'user_name' => $userData['user']['name'],
                'opening_balance' => $userData['opening_balance'],
                'total_debits' => $totalDebits,
                'total_credits' => $totalCredits,
                'closing_balance' => $userData['closing_balance'],
                'transaction_count' => count($userData['transactions']),
            ]);
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'User Name',
            'Opening Balance',
            'Total Advances',
            'Total Returns/Expenses',
            'Closing Balance',
            'Transaction Count',
        ];
    }

    public function map($row): array
    {
        return [
            $row->user_name,
            $row->opening_balance * $this->data['exchange_rate'],
            $row->total_debits * $this->data['exchange_rate'],
            $row->total_credits * $this->data['exchange_rate'],
            $row->closing_balance * $this->data['exchange_rate'],
            $row->transaction_count,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'C' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'D' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'E' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function title(): string
    {
        return 'Summary';
    }
}

class AdvanceCashUserSheet implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    private $userData;

    private $period;

    private $exchangeRate;

    private $currencyCode;

    public function __construct($userData, $period, $exchangeRate, $currencyCode)
    {
        $this->userData = $userData;
        $this->period = $period;
        $this->exchangeRate = $exchangeRate;
        $this->currencyCode = $currencyCode;
    }

    public function collection()
    {
        $collection = collect();

        // Add opening balance row if not zero
        if ($this->userData['opening_balance'] != 0) {
            $collection->push((object) [
                'date' => Carbon::parse($this->period['start'])->subDay(),
                'details' => 'Opening Balance',
                'ref_number' => null,
                'debit' => null,
                'credit' => null,
                'balance' => $this->userData['opening_balance'],
                'is_opening' => true,
            ]);
        }

        // Add transactions
        foreach ($this->userData['transactions'] as $transaction) {
            $collection->push((object) [
                'date' => Carbon::parse($transaction['date']),
                'details' => $transaction['details'],
                'ref_number' => $transaction['ref_number'],
                'debit' => $transaction['debit'] > 0 ? $transaction['debit'] : null,
                'credit' => $transaction['credit'] > 0 ? $transaction['credit'] : null,
                'balance' => $transaction['balance'],
                'is_opening' => false,
            ]);
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Date',
            'Description',
            'Ref #',
            'Advance (Debit)',
            'Return/Expense (Credit)',
            'Balance',
        ];
    }

    public function map($row): array
    {
        return [
            Date::dateTimeToExcel($row->date),
            $row->details,
            $row->ref_number,
            $row->debit ? $row->debit * $this->exchangeRate : null,
            $row->credit ? $row->credit * $this->exchangeRate : null,
            $row->balance * $this->exchangeRate,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'D' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'E' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'F' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function title(): string
    {
        return substr($this->userData['user']['name'], 0, 31);
    }
}
