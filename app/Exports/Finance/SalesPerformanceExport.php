<?php

declare(strict_types=1);

namespace App\Exports\Finance;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class SalesPerformanceExport implements WithMultipleSheets
{
    use Exportable;

    private $data;

    private $period;

    private $fileName;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
        $this->fileName = 'sales-performance-report-' . $period['start'] . '-to-' . $period['end'] . '.xlsx';
    }

    public function sheets(): array
    {
        $sheets = [];

        // Summary sheet
        $sheets[] = new SalesPerformanceSummarySheet($this->data, $this->period);

        // Grouped data sheet
        $sheets[] = new SalesPerformanceGroupedDataSheet($this->data, $this->period);

        // Top customers sheet
        $sheets[] = new SalesPerformanceTopCustomersSheet($this->data, $this->period);

        // Payment trends sheet
        $sheets[] = new SalesPerformancePaymentTrendsSheet($this->data, $this->period);

        // Status breakdown sheet
        $sheets[] = new SalesPerformanceStatusBreakdownSheet($this->data, $this->period);

        return $sheets;
    }
}

class SalesPerformanceSummarySheet implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    private $data;

    private $period;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
    }

    public function collection()
    {
        $collection = collect();

        $summary = $this->data['summary'];

        $collection->push((object) [
            'metric' => 'Total Invoices',
            'value' => $summary['total_invoices'],
            'type' => 'count',
        ]);

        $collection->push((object) [
            'metric' => 'Total Sales',
            'value' => $summary['total_sales'],
            'type' => 'currency',
        ]);

        $collection->push((object) [
            'metric' => 'Total Paid',
            'value' => $summary['total_paid'],
            'type' => 'currency',
        ]);

        $collection->push((object) [
            'metric' => 'Total Outstanding',
            'value' => $summary['total_outstanding'],
            'type' => 'currency',
        ]);

        $collection->push((object) [
            'metric' => 'Average Invoice Value',
            'value' => $summary['average_invoice_value'],
            'type' => 'currency',
        ]);

        $collection->push((object) [
            'metric' => 'Payment Rate',
            'value' => $summary['payment_rate'],
            'type' => 'percentage',
        ]);

        $collection->push((object) [
            'metric' => 'Unique Customers',
            'value' => $summary['unique_customers'],
            'type' => 'count',
        ]);

        $collection->push((object) [
            'metric' => 'Unique Groups',
            'value' => $summary['unique_groups'],
            'type' => 'count',
        ]);

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Metric',
            'Value',
        ];
    }

    public function map($row): array
    {
        $value = $row->value;

        if ($row->type === 'currency') {
            $currencyCode = $this->data['currency_code'] ?? 'SAR';
            $value = $currencyCode . ' ' . number_format($value, 2);
        } elseif ($row->type === 'percentage') {
            $value = number_format($value, 2) . '%';
        }

        return [
            $row->metric,
            $value,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_TEXT,
        ];
    }

    public function title(): string
    {
        return 'Summary';
    }
}

class SalesPerformanceGroupedDataSheet implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    private $data;

    private $period;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
    }

    public function collection()
    {
        $collection = collect();

        foreach ($this->data['grouped_data'] as $group) {
            $collection->push((object) [
                'label' => $group['label'],
                'count' => $group['count'],
                'sales' => $group['sales'],
                'paid' => $group['paid'],
                'outstanding' => $group['outstanding'],
            ]);
        }

        return $collection;
    }

    public function headings(): array
    {
        $groupBy = ucfirst($this->data['group_by'] ?? 'Group');

        return [
            $groupBy,
            'Invoice Count',
            'Total Sales',
            'Total Paid',
            'Outstanding',
        ];
    }

    public function map($row): array
    {
        return [
            $row->label,
            $row->count,
            $row->sales,
            $row->paid,
            $row->outstanding,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'C' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'D' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'E' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function title(): string
    {
        $groupBy = ucfirst($this->data['group_by'] ?? 'Group');

        return "Grouped by {$groupBy}";
    }
}

class SalesPerformanceTopCustomersSheet implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    private $data;

    private $period;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
    }

    public function collection()
    {
        $collection = collect();

        foreach ($this->data['top_customers'] as $customer) {
            $collection->push((object) [
                'name' => $customer['name'],
                'sales' => $customer['sales'],
                'invoices' => $customer['invoices'],
                'payment_rate' => $customer['payment_rate'],
            ]);
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Customer Name',
            'Total Sales',
            'Invoice Count',
            'Payment Rate (%)',
        ];
    }

    public function map($row): array
    {
        return [
            $row->name,
            $row->sales,
            $row->invoices,
            $row->payment_rate,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'D' => NumberFormat::FORMAT_NUMBER_00,
        ];
    }

    public function title(): string
    {
        return 'Top Customers';
    }
}

class SalesPerformancePaymentTrendsSheet implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    private $data;

    private $period;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
    }

    public function collection()
    {
        $collection = collect();

        foreach ($this->data['payment_trends'] as $trend) {
            $collection->push((object) [
                'month' => $trend['month'],
                'amount' => $trend['amount'],
                'count' => $trend['count'],
            ]);
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Month',
            'Payment Amount',
            'Payment Count',
        ];
    }

    public function map($row): array
    {
        return [
            $row->month,
            $row->amount,
            $row->count,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function title(): string
    {
        return 'Payment Trends';
    }
}

class SalesPerformanceStatusBreakdownSheet implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    private $data;

    private $period;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
    }

    public function collection()
    {
        $collection = collect();

        foreach ($this->data['status_breakdown'] as $status) {
            $collection->push((object) [
                'status' => $status['status'],
                'count' => $status['count'],
                'percentage' => $status['percentage'],
                'sales' => $status['sales'],
            ]);
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Status',
            'Invoice Count',
            'Percentage (%)',
            'Total Sales',
        ];
    }

    public function map($row): array
    {
        return [
            $row->status,
            $row->count,
            $row->percentage,
            $row->sales,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'C' => NumberFormat::FORMAT_NUMBER_00,
            'D' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function title(): string
    {
        return 'Status Breakdown';
    }
}
