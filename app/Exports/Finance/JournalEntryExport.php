<?php

declare(strict_types=1);

namespace App\Exports\Finance;

use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class JournalEntryExport implements WithMultipleSheets
{
    use Exportable;

    private $data;

    private $period;

    private $fileName;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
        $this->fileName = 'journal-entry-report-' . $period['start'] . '-to-' . $period['end'] . '.xlsx';
    }

    public function sheets(): array
    {
        $sheets = [];

        // Summary sheet
        $sheets[] = new JournalEntrySummarySheet($this->data, $this->period);

        // Detailed entries sheet
        $sheets[] = new JournalEntryDetailSheet($this->data, $this->period);

        return $sheets;
    }
}

class JournalEntrySummarySheet implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    private $data;

    private $period;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
    }

    public function collection()
    {
        $collection = collect();

        foreach ($this->data['entries'] as $entry) {
            $collection->push((object) [
                'entry_id' => $entry['entry_id'],
                'entry_date' => Carbon::parse($entry['entry_date']),
                'details' => $entry['details'],
                'transaction_type' => $entry['transaction_type'],
                'transaction_number' => $entry['transaction_number'],
                'total_debits' => $entry['entry_debits'],
                'total_credits' => $entry['entry_credits'],
                'is_balanced' => abs($entry['entry_debits'] - $entry['entry_credits']) <= 0.01,
                'item_count' => count($entry['items']),
            ]);
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Entry ID',
            'Date',
            'Description',
            'Transaction Type',
            'Transaction Number',
            'Total Debits',
            'Total Credits',
            'Balanced',
            'Item Count',
        ];
    }

    public function map($row): array
    {
        return [
            $row->entry_id,
            Date::dateTimeToExcel($row->entry_date),
            $row->details,
            $row->transaction_type,
            $row->transaction_number,
            $row->total_debits * $this->data['exchange_rate'],
            $row->total_credits * $this->data['exchange_rate'],
            $row->is_balanced ? 'Yes' : 'No',
            $row->item_count,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'F' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'G' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function title(): string
    {
        return 'Summary';
    }
}

class JournalEntryDetailSheet implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    private $data;

    private $period;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
    }

    public function collection()
    {
        $collection = collect();

        foreach ($this->data['entries'] as $entry) {
            foreach ($entry['items'] as $item) {
                $collection->push((object) [
                    'entry_id' => $entry['entry_id'],
                    'entry_date' => Carbon::parse($entry['entry_date']),
                    'details' => $entry['details'],
                    'transaction_type' => $entry['transaction_type'],
                    'transaction_number' => $entry['transaction_number'],
                    'account_code' => $item['account_code'],
                    'account_name' => $item['account_name'],
                    'debit' => $item['debit'] > 0 ? $item['debit'] : null,
                    'credit' => $item['credit'] > 0 ? $item['credit'] : null,
                ]);
            }
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Entry ID',
            'Date',
            'Description',
            'Transaction Type',
            'Transaction Number',
            'Account Code',
            'Account Name',
            'Debit',
            'Credit',
        ];
    }

    public function map($row): array
    {
        return [
            $row->entry_id,
            Date::dateTimeToExcel($row->entry_date),
            $row->details,
            $row->transaction_type,
            $row->transaction_number,
            $row->account_code,
            $row->account_name,
            $row->debit ? $row->debit * $this->data['exchange_rate'] : null,
            $row->credit ? $row->credit * $this->data['exchange_rate'] : null,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'H' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'I' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function title(): string
    {
        return 'Detail';
    }
}
