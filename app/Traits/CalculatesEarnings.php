<?php

namespace App\Traits;

use App\Enums\Finance\AccountCategory;
use App\Models\Finance\JournalEntryItem;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

trait CalculatesEarnings
{
    /**
     * Calculate earnings (revenue - expenses) for a given period
     */
    protected function calculateEarnings(Carbon $start, Carbon $end): array
    {
        // Get revenues
        $revenues = JournalEntryItem::query()
            ->join('cash_accounts', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
            ->join('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
            ->whereBetween('journal_entries.entry_date', [$start, $end])
            ->whereIn('cash_accounts.category', [
                AccountCategory::OperatingRevenue->value,
                AccountCategory::UncategorizedRevenue->value,
            ])
            ->select([
                DB::raw('SUM(CASE WHEN type = "d" THEN amount ELSE 0 END) as total_debit'),
                DB::raw('SUM(CASE WHEN type = "c" THEN amount ELSE 0 END) as total_credit'),
            ])
            ->first();

        // Get expenses
        $expenses = JournalEntryItem::query()
            ->join('cash_accounts', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
            ->join('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
            ->whereBetween('journal_entries.entry_date', [$start, $end])
            ->whereIn('cash_accounts.category', [
                AccountCategory::CostOfGoodsSold->value,
                AccountCategory::OperatingExpense->value,
                AccountCategory::CostOfSales->value,
                AccountCategory::NonOperatingExpense->value,
                AccountCategory::UncategorizedExpense->value,
            ])
            ->select([
                DB::raw('SUM(CASE WHEN type = "d" THEN amount ELSE 0 END) as total_debit'),
                DB::raw('SUM(CASE WHEN type = "c" THEN amount ELSE 0 END) as total_credit'),
            ])
            ->first();

        $totalRevenue = ($revenues ? $revenues->total_credit - $revenues->total_debit : 0);
        $totalExpense = ($expenses ? $expenses->total_debit - $expenses->total_credit : 0);
        $netIncome = $totalRevenue - $totalExpense;

        return [
            'total_revenue' => $totalRevenue,
            'total_expense' => $totalExpense,
            'net_income' => $netIncome,
        ];
    }

    /**
     * Calculate earnings for a period before the given date
     */
    protected function calculatePreviousPeriodEarnings(Carbon $startDate): float
    {
        // Calculate revenues before start date
        $previousRevenues = JournalEntryItem::query()
            ->join('cash_accounts', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
            ->join('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
            ->where('journal_entries.entry_date', '<', $startDate)
            ->whereIn('cash_accounts.category', [
                AccountCategory::OperatingRevenue->value,
                AccountCategory::UncategorizedRevenue->value,
            ])
            ->select([
                DB::raw('SUM(CASE WHEN type = "d" THEN amount ELSE 0 END) as total_debit'),
                DB::raw('SUM(CASE WHEN type = "c" THEN amount ELSE 0 END) as total_credit'),
            ])
            ->first();

        // Calculate expenses before start date
        $previousExpenses = JournalEntryItem::query()
            ->join('cash_accounts', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
            ->join('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
            ->where('journal_entries.entry_date', '<', $startDate)
            ->whereIn('cash_accounts.category', [
                AccountCategory::CostOfGoodsSold->value,
                AccountCategory::OperatingExpense->value,
                AccountCategory::CostOfSales->value,
                AccountCategory::NonOperatingExpense->value,
                AccountCategory::UncategorizedExpense->value,
            ])
            ->select([
                DB::raw('SUM(CASE WHEN type = "d" THEN amount ELSE 0 END) as total_debit'),
                DB::raw('SUM(CASE WHEN type = "c" THEN amount ELSE 0 END) as total_credit'),
            ])
            ->first();

        $totalPreviousRevenue = ($previousRevenues ? $previousRevenues->total_credit - $previousRevenues->total_debit : 0);
        $totalPreviousExpense = ($previousExpenses ? $previousExpenses->total_debit - $previousExpenses->total_credit : 0);

        return $totalPreviousRevenue - $totalPreviousExpense;
    }

    /**
     * Get detailed revenue accounts with balances
     */
    protected function getRevenueAccounts(Carbon $start, Carbon $end): array
    {
        return JournalEntryItem::query()
            ->join('cash_accounts', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
            ->join('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
            ->whereBetween('journal_entries.entry_date', [$start, $end])
            ->whereIn('cash_accounts.category', [
                AccountCategory::OperatingRevenue->value,
                AccountCategory::UncategorizedRevenue->value,
            ])
            ->select([
                'cash_accounts.code',
                'cash_accounts.name',
                'cash_accounts.category',
                DB::raw('SUM(CASE WHEN type = "d" THEN amount ELSE 0 END) as total_debit'),
                DB::raw('SUM(CASE WHEN type = "c" THEN amount ELSE 0 END) as total_credit'),
            ])
            ->groupBy('cash_accounts.code', 'cash_accounts.name', 'cash_accounts.category')
            ->orderBy('cash_accounts.code')
            ->get()
            ->map(function ($account) {
                // For revenue accounts (normal credit balance)
                $balance = $account->total_credit - $account->total_debit;

                return [
                    'name' => $account->name,
                    'category' => AccountCategory::from($account->category)->getLabel(),
                    'amount' => $balance,
                ];
            })
            ->groupBy('category')
            ->toArray();
    }

    /**
     * Get detailed expense accounts with balances
     */
    protected function getExpenseAccounts(Carbon $start, Carbon $end): array
    {
        return JournalEntryItem::query()
            ->join('cash_accounts', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
            ->join('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
            ->whereBetween('journal_entries.entry_date', [$start, $end])
            ->whereIn('cash_accounts.category', [
                AccountCategory::CostOfGoodsSold->value,
                AccountCategory::OperatingExpense->value,
                AccountCategory::CostOfSales->value,
                AccountCategory::NonOperatingExpense->value,
                AccountCategory::UncategorizedExpense->value,
            ])
            ->select([
                'cash_accounts.code',
                'cash_accounts.name',
                'cash_accounts.category',
                DB::raw('SUM(CASE WHEN type = "d" THEN amount ELSE 0 END) as total_debit'),
                DB::raw('SUM(CASE WHEN type = "c" THEN amount ELSE 0 END) as total_credit'),
            ])
            ->groupBy('cash_accounts.code', 'cash_accounts.name', 'cash_accounts.category')
            ->orderBy('cash_accounts.code')
            ->get()
            ->map(function ($account) {
                // For expense accounts (normal debit balance)
                $balance = $account->total_debit - $account->total_credit;

                return [
                    'name' => $account->name,
                    'category' => AccountCategory::from($account->category)->getLabel(),
                    'amount' => $balance,
                ];
            })
            ->groupBy('category')
            ->toArray();
    }
}
