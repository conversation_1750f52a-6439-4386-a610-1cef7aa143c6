<?php

namespace App\Settings;

use <PERSON><PERSON>\LaravelSettings\Settings;

class FinanceSettings extends Settings
{
    public string $invoice_default_terms;

    public static function group(): string
    {
        return 'finance';
    }

    public static function getDefaults(): array
    {
        return [
            'invoice_default_terms' => '1. Reservation policy
- Check in after 17:00 (Local time), Check out before 14:00 (Local time).
- Urgent reservation/hotel booking on, or less than 3 days before check-in date trodden as a guarantee, booking and full payment should be done on the invoice date.

2. Amendment and Cancellation policy:
- Amendment to reduce room maximum 15 days before check-in date.
- Amendment to change room type maximum 7 days before check-in date.
- The rooming list should be submitted 7 days before check-in date.
- Cancellation 12 days before check-in will be charged as per hotel term condition.

3. Payment policy:
- 30 % payment upon invoice due date as definite booking.
- Deposit received is non-refundable.
- Full payment should be done before check-in date.',
        ];
    }
}
