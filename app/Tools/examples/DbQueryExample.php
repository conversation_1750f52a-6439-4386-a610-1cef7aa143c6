<?php

namespace App\Tools\Examples;

use App\Tools\DbQuery;
use Prism\Prism\Prism;
use Prism\Prism\Enums\Provider;

/**
 * Example usage of the DbQuery tool with Prism
 */
class DbQueryExample
{
    public function basicUsage()
    {
        // Create the database query tool
        $dbQueryTool = new DbQuery();

        // Use with Prism to query customer information
        $response = Prism::text()
            ->using(Provider::OpenAI, 'gpt-4')
            ->withMaxSteps(3)
            ->withPrompt('How many customers do we have from Indonesia?')
            ->withTools([$dbQueryTool])
            ->asText();

        return $response->text;
    }

    public function complexQuery()
    {
        $dbQueryTool = new DbQuery();

        // More complex query involving multiple tables
        $response = Prism::text()
            ->using(Provider::OpenAI, 'gpt-4')
            ->withMaxSteps(3)
            ->withPrompt('Show me all groups departing next month with their customer details and total pilgrim count')
            ->withTools([$dbQueryTool])
            ->asText();

        return $response->text;
    }

    public function financialQuery()
    {
        $dbQueryTool = new DbQuery();

        // Financial analysis query
        $response = Prism::text()
            ->using(Provider::OpenAI, 'gpt-4')
            ->withMaxSteps(3)
            ->withPrompt('What is our total revenue this month from paid invoices?')
            ->withTools([$dbQueryTool])
            ->asText();

        return $response->text;
    }

    public function multipleQueries()
    {
        $dbQueryTool = new DbQuery();

        // Query that might require multiple database calls
        $response = Prism::text()
            ->using(Provider::OpenAI, 'gpt-4')
            ->withMaxSteps(5) // Allow more steps for multiple queries
            ->withPrompt('Give me a summary of our business: total customers, active groups, upcoming departures, and revenue this month')
            ->withTools([$dbQueryTool])
            ->asText();

        return $response->text;
    }

    public function inspectToolUsage()
    {
        $dbQueryTool = new DbQuery();

        $response = Prism::text()
            ->using(Provider::OpenAI, 'gpt-4')
            ->withMaxSteps(3)
            ->withPrompt('How many pilgrims are traveling next week?')
            ->withTools([$dbQueryTool])
            ->asText();

        // Inspect how the tool was used
        foreach ($response->steps as $step) {
            if ($step->toolCalls) {
                foreach ($step->toolCalls as $toolCall) {
                    echo "Tool: " . $toolCall->name . "\n";
                    echo "Arguments: " . json_encode($toolCall->arguments()) . "\n";
                }
            }
        }

        // Check tool results
        if ($response->toolResults) {
            foreach ($response->toolResults as $toolResult) {
                echo "Tool: " . $toolResult->toolName . "\n";
                echo "Result: " . $toolResult->result . "\n";
            }
        }

        return $response->text;
    }
}
