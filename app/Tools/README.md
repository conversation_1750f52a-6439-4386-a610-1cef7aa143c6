# Prism Tools

This directory contains custom tools for use with Prism PHP, extending AI capabilities with specific business functions.

## DbQuery Tool

The `DbQuery` tool allows AI assistants to query the Umrah service database using natural language. It leverages the existing `AiChatService` to provide safe, read-only access to business data.

### Features

- **Natural Language Queries**: Convert human language into structured database queries
- **Read-Only Access**: Ensures data safety by only allowing SELECT operations
- **Schema-Aware**: Understands the database structure and relationships
- **Security**: Only allows queries on pre-approved tables
- **Intelligent Responses**: Provides contextual answers based on query results

### Usage Example

```php
use App\Tools\DbQuery;
use Prism\Prism\Prism;
use Prism\Prism\Enums\Provider;

// Create the database query tool
$dbQueryTool = new DbQuery();

// Use with Prism
$response = Prism::text()
    ->using(Provider::OpenAI, 'gpt-4')
    ->withMaxSteps(3)
    ->withPrompt('How many customers do we have from Indonesia?')
    ->withTools([$dbQueryTool])
    ->asText();

echo $response->text;
```

### Supported Query Types

The tool can handle various types of database queries:

1. **Customer Information**
   - "Show me all customers from Indonesia"
   - "How many customers do we have?"
   - "Find customers who haven't made any bookings"

2. **Group and Pilgrim Data**
   - "List all groups departing next month"
   - "How many pilgrims are in group ABC123?"
   - "Show me groups with more than 20 pilgrims"

3. **Financial Information**
   - "What's the total revenue this month?"
   - "Show unpaid invoices"
   - "List all bills from vendor XYZ"

4. **Hotel and Flight Information**
   - "Which hotels are booked for next week?"
   - "Show all flights departing from Jakarta"
   - "List room assignments for group ABC123"

5. **Itinerary and Schedule Data**
   - "What activities are scheduled for tomorrow?"
   - "Show itinerary for group ABC123"
   - "List all upcoming departures"

### Allowed Tables

The tool has access to the following database tables:

- `customers`, `groups`, `pilgrims`
- `hotels`, `rooms`, `flights`
- `invoices`, `bills`, `estimates`
- `itineraries`, `contacts`
- `vendors`, `agents`, `airlines`
- And many more (see `config/ai-chat.php`)

### Security Features

- **Table Whitelist**: Only pre-approved tables can be queried
- **Read-Only Operations**: No INSERT, UPDATE, DELETE, or DDL operations
- **SQL Injection Protection**: All queries are parameterized
- **Result Limits**: Maximum 100 records per query
- **Expression Validation**: Raw SQL expressions are validated for safety

### Error Handling

The tool provides clear error messages for:
- Invalid table access attempts
- Malformed queries
- Database connection issues
- Unauthorized operations

### Integration with Existing Services

The `DbQuery` tool reuses the existing `AiChatService` infrastructure, ensuring:
- Consistent behavior with the current AI chat feature
- Shared configuration and security settings
- Unified logging and error handling
- Cache optimization for schema information

### Configuration

Tool behavior is controlled by the `config/ai-chat.php` configuration file:

```php
return [
    'allowed_tables' => [
        'customers',
        'groups',
        'pilgrims',
        // ... more tables
    ],
    'max_results' => 100,
];
```

### Advanced Usage

For more complex scenarios, you can combine multiple tools:

```php
use App\Tools\DbQuery;
use Prism\Prism\Prism;
use Prism\Prism\Enums\Provider;

$dbQueryTool = new DbQuery();
// Add other tools as needed

$response = Prism::text()
    ->using(Provider::OpenAI, 'gpt-4')
    ->withMaxSteps(5)
    ->withPrompt('Analyze our customer data and suggest improvements for our services')
    ->withTools([$dbQueryTool])
    ->asText();
```

### Best Practices

1. **Be Specific**: Provide clear, specific queries for better results
2. **Use Context**: Include relevant context in your prompts
3. **Handle Errors**: Always check for error responses
4. **Limit Scope**: Keep queries focused on specific business needs
5. **Monitor Usage**: Log and monitor tool usage for optimization

### Troubleshooting

Common issues and solutions:

1. **"Table not allowed"**: Check if the table is in the allowed_tables config
2. **"Invalid query"**: Ensure the natural language query is clear and specific
3. **"No results"**: Verify the data exists and query criteria are correct
4. **"Connection error"**: Check database connectivity and credentials
