<?php

namespace App\Models;

use App\Models\Finance\UserCash;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lab404\Impersonate\Services\ImpersonateManager;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements FilamentUser
{
    use HasApiTokens, HasFactory, Notifiable;
    use HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'password',

        'staff_id', // @deprecated

        'last_login_at',
        'last_login_ip',

        'meta',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'last_login_at' => 'datetime',
            'meta' => 'array',
        ];
    }

    public function canAccessPanel(Panel $panel): bool
    {
        if ($panel->getId() == 'finance') {
            return $this->email_verified_at != null && $this->hasRole(['Admin', 'Finance']);
        }

        return app(ImpersonateManager::class)->isImpersonating() ||
            $this->id === 1 ||
            $this->email_verified_at != null;
    }

    public function canImpersonate()
    {
        return $this->hasRole(['Admin']);
    }

    public function canBeImpersonated()
    {
        return ! $this->isSuperAdmin();
    }

    public function isSuperAdmin(): bool
    {
        return $this->id === 1;
    }

    public function rememberTokens(): HasMany
    {
        return $this->hasMany(RememberToken::class);
    }

    /**
     * @deprecated
     **/
    public function staff(): BelongsTo
    {
        return $this->belongsTo(Staff::class);
    }

    public function vendors(): BelongsToMany
    {
        return $this->belongsToMany(Vendor::class);
    }

    public function userCashes(): HasMany
    {
        return $this->hasMany(UserCash::class);
    }

    public function delete()
    {
        if (
            Group::query()
                ->where('mutawif_id', $this->id)
                ->orWhere('mutawif_2_id', $this->id)
                ->orWhere('mutawif_3_id', $this->id)
                ->count() > 0
        ) {
            return null;
        }

        return parent::delete();
    }

    public function scopeVerified(Builder $query): Builder
    {
        return $query->whereNotNull('email_verified_at');
    }

    public function getRememberToken()
    {
        $host = request()->host();

        return $this->rememberTokens()->where('host', $host)->value('token');
    }

    public function setRememberToken($value)
    {
        $host = request()->host();
        $this->rememberTokens()->updateOrCreate([
            'host' => $host,
        ], [
            'token' => $value,
        ]);
    }
}
