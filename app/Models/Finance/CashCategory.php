<?php

namespace App\Models\Finance;

use App\Enums\ExpenseGroup;
use App\Models\GroupCash;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CashCategory extends Model
{
    protected $fillable = [
        'parent_id',
        'group',
        'type', // in / out
        'name',
        'account_id',
    ];

    protected function casts()
    {
        return [
            'group' => ExpenseGroup::class,
        ];
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(static::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(static::class, 'parent_id');
    }

    public function group_cashes(): Has<PERSON><PERSON>
    {
        return $this->hasMany(GroupCash::class, 'category_id');
    }

    public function user_cashes(): Has<PERSON>any
    {
        return $this->hasMany(UserCash::class, 'category_id');
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(CashAccount::class);
    }
}
