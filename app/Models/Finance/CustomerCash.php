<?php

namespace App\Models\Finance;

use App\Actions\Finance\GenerateDepositPDF;
use App\Mail\CustomerDepositReceipt;
use App\Models\Contracts\JournalTransaction;
use App\Models\Customer;
use App\Models\Traits\DefaultLogOptions;
use App\Models\Traits\WithUserstamps;
use Filament\Forms;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\Mail;
use Spatie\Activitylog\Traits\LogsActivity;

class CustomerCash extends Model implements JournalTransaction
{
    use DefaultLogOptions;
    use HasFactory;
    use LogsActivity;
    use WithUserstamps;

    protected $fillable = [
        'customer_id',

        'cashed_at',

        'cash_account_id',

        'type', // d / c
        'amount',
        'currency',
        'exchange_rate',

        'details',
        'attachment',

        'related_type',
        'related_id',

        'created_by_id',
        'updated_by_id',
    ];

    protected $casts = [
        'cashed_at' => 'datetime',
        'amount' => 'float',
        'exchange_rate' => 'float',
    ];

    public function getTransactionType(): string
    {
        return 'Customer Deposit';
    }

    public function getTransactionNumber(): string
    {
        return '#' . str_pad($this->id, 6, '0', STR_PAD_LEFT);
    }

    public function getTransactionDetailAttribute(): ?string
    {
        return $this->details;
    }

    protected static function booted()
    {
        static::creating(function ($model) {
            $model->exchange_rate ??= 1;
        });
        static::saved(function ($model) {
            $model->syncJournalEntry();
        });
    }

    public function cashAccount(): BelongsTo
    {
        return $this->belongsTo(CashAccount::class, 'cash_account_id')->isCashOrBank();
    }

    public function relatedDetails(): Attribute
    {
        return Attribute::make(
            get: fn () => match ($this->related_type) {
                'invoice_payment' => 'Invoice Payment ' . $this->related?->invoice?->invoice_number . " (#{$this->related_id})",
                default => null,
            },
        );
    }

    public function invoice(): Attribute
    {
        return Attribute::get(fn () => match ($this->related_type) {
            'invoice_payment' => $this->related?->invoice,
            default => null,
        });
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function journal_entry(): MorphOne
    {
        return $this->morphOne(JournalEntry::class, 'transaction');
    }

    public function related(): MorphTo
    {
        return $this->morphTo();
    }

    public function syncJournalEntry(): void
    {
        if (filled($this->related_type) && filled($this->related_id)) {
            $this->journal_entry?->delete();

            return;
        }

        /** @var JournalEntry $entry */
        $entry = $this->journal_entry()->updateOrCreate([], [
            'entry_date' => $this->cashed_at,
            'details' => $this->details,
        ]);
        $cashAccountId = $this->cash_account_id ?? CashAccount::query()
            ->where('code', config('finance.coa.main_cash'))
            ->value('id');
        $depositAccountId = CashAccount::query()
            ->where('code', config('finance.coa.customer_deposit'))
            ->value('id');
        if ($this->type === 'd') {
            $entry->items()->updateOrCreate([
                'type' => 'd',
            ], [
                'account_id' => $cashAccountId,
                'amount' => $this->amount * $this->exchange_rate,
            ]);
            $entry->items()->updateOrCreate([
                'type' => 'c',
            ], [
                'account_id' => $depositAccountId,
                'amount' => $this->amount * $this->exchange_rate,
                'owner_type' => 'customer',
                'owner_id' => $this->customer_id,
            ]);
        } else {
            $entry->items()->updateOrCreate([
                'type' => 'd',
            ], [
                'account_id' => $depositAccountId,
                'amount' => $this->amount * $this->exchange_rate,
                'owner_type' => 'customer',
                'owner_id' => $this->customer_id,
            ]);
            $entry->items()->updateOrCreate([
                'type' => 'c',
            ], [
                'account_id' => $cashAccountId,
                'amount' => $this->amount * $this->exchange_rate,
            ]);
        }
    }

    public function delete()
    {
        $this->journal_entry?->delete();

        return parent::delete();
    }

    public static function getReceiptTableAction()
    {
        return Tables\Actions\ViewAction::make()
            ->label('Receipt')
            ->icon('heroicon-m-receipt-percent')
            ->form(function ($record) {
                return [
                    Forms\Components\View::make('payment')
                        ->view('components.finance.deposit-receipt')
                        ->viewData(['deposit' => $record, 'isInline' => true]),
                ];
            })
            ->extraModalFooterActions([
                Tables\Actions\Action::make('pdf')
                    ->label('PDF')
                    ->action(function ($record) {
                        return response()->download(GenerateDepositPDF::run($record));
                    })
                    ->color('gray')
                    ->icon('heroicon-o-arrow-down-tray'),
                Tables\Actions\Action::make('send')
                    ->action(function ($record, $data, $action) {
                        Mail::to($data['email'])
                            ->send(
                                new CustomerDepositReceipt($record)
                            );
                        $action->success();
                    })
                    ->successNotificationTitle('Deposit receipt sent.')
                    ->form(function ($record) {
                        return [
                            Forms\Components\TextInput::make('email')
                                ->required()
                                ->email()
                                ->default($record->customer->email),
                        ];
                    })
                    ->modalWidth('sm')
                    ->modalHeading('Send receipt')
                    ->modalSubmitActionLabel('Send')
                    ->color('success')
                    ->icon('heroicon-o-paper-airplane'),
            ])
            ->modalFooterActionsAlignment(Alignment::End)
            ->modalHeading('')
            ->visible(fn ($record) => $record->type === 'd');
    }
}
