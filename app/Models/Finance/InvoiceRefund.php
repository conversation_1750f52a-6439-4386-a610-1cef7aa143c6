<?php

namespace App\Models\Finance;

use App\Enums\PaymentMethod;
use App\Models\Concerns\HasJournalEntry;
use App\Models\Contracts\JournalTransaction;
use App\Models\Traits\DefaultLogOptions;
use App\Models\Traits\WithUserstamps;
use Filament\Forms;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\Traits\LogsActivity;

class InvoiceRefund extends Model implements JournalTransaction
{
    use DefaultLogOptions;
    use HasFactory;
    use HasJournalEntry;
    use LogsActivity;
    use WithUserstamps;

    protected $fillable = [
        'invoice_id',

        'payment_method',
        'cash_account_id',

        'customer_cash_id',

        'refunded_at',
        'amount',
        'currency_code',
        'exchange_rate',

        'description',
        'attachment',

        'created_by_id',
        'updated_by_id',
    ];

    protected $casts = [
        'payment_method' => PaymentMethod::class,
        'amount' => 'float',
        'refunded_at' => 'datetime',
    ];

    public function getTransactionType(): string
    {
        return 'Invoice Refund';
    }

    public function getTransactionNumber(): string
    {
        return '#' . str_pad($this->id, 6, '0', STR_PAD_LEFT);
    }

    public function getTransactionDetailAttribute(): ?string
    {
        return $this->description;
    }

    protected static function boot()
    {
        static::creating(function (self $model) {
            $model->exchange_rate ??= 1;
        });
        static::saving(function (self $model) {
            if ($model->payment_method === PaymentMethod::Deposit) {
                $model->cash_account_id = CashAccount::query()
                    ->where('code', config('finance.coa.customer_deposit'))
                    ->value('id');
            } else {
                $model->cash_account_id ??= CashAccount::query()
                    ->where('code', config('finance.coa.main_cash'))
                    ->value('id');

                $model->payment_method ??= PaymentMethod::from(CashAccount::query()
                    ->find($model->cash_account_id)?->group ?? 'cash');
            }
        });
        static::saved(function (self $model) {
            if ($model->payment_method === PaymentMethod::Deposit && blank($model->customer_cash_id)) {
                $model->updateQuietly([
                    'customer_cash_id' => CustomerCash::query()
                        ->create([
                            'customer_id' => $model->invoice?->customer_id,

                            'cashed_at' => $model->refunded_at,

                            'type' => 'd',
                            'amount' => $model->amount,
                            'currency' => $model->currency_code,
                            'exchange_rate' => $model->exchange_rate,

                            'details' => $model->description,
                            'attachment' => $model->attachment,

                            'related_type' => 'invoice_refund',
                            'related_id' => $model->id,
                        ])
                        ->id,
                ]);
            } elseif ($model->payment_method !== PaymentMethod::Deposit && filled($model->customer_cash_id)) {
                CustomerCash::find($model->customer_cash_id)?->update([
                    'related_type' => null,
                    'related_id' => null,
                ]);
                $model->updateQuietly([
                    'customer_cash_id' => null,
                ]);
            }

            $model->invoice?->save();
            $model->syncJournalEntry();
        });

        parent::boot();
    }

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function cash_account(): BelongsTo
    {
        return $this->belongsTo(CashAccount::class);
    }

    public function customer_cash(): BelongsTo
    {
        return $this->belongsTo(CustomerCash::class);
    }

    public function syncJournalEntry(): void
    {
        /** @var JournalEntry $entry */
        $entry = $this->journal_entry()->updateOrCreate([], [
            'entry_date' => $this->refunded_at,
            'details' => 'Invoice Refund ' . ($this->invoice?->invoice_number ?? '') . " (#{$this->id})",
        ]);
        $customerCash = $this->customer_cash;
        $entry->items()->delete();
        $entry->items()->create([
            'type' => 'd',
            'account_id' => CashAccount::query()
                ->where('code', config('finance.coa.sales_returns'))
                ->value('id'),
            'amount' => $this->amount * $this->exchange_rate,
        ]);
        $entry->items()->create([
            'type' => 'c',
            'account_id' => $this->cash_account_id ?? CashAccount::query()
                ->where('code', $this->payment_method === PaymentMethod::Deposit
                    ? config('finance.coa.customer_deposit')
                    : config('finance.coa.main_cash'))
                ->value('id'),
            'amount' => $this->amount * $this->exchange_rate,
            'owner_type' => $this->payment_method === PaymentMethod::Deposit ? 'customer' : null,
            'owner_id' => $this->payment_method === PaymentMethod::Deposit ? $customerCash?->customer_id : null,
        ]);
    }

    public function delete()
    {
        $this->customer_cash?->delete();
        $this->journal_entry?->delete();

        return parent::delete();
    }

    public static function getReceiptTableAction()
    {
        return Tables\Actions\ViewAction::make()
            ->label('Receipt')
            ->icon('heroicon-m-receipt-percent')
            ->form(function ($record) {
                return [
                    Forms\Components\View::make('payment')
                        ->view('components.finance.invoice-refund-receipt')
                        ->viewData(['refund' => $record, 'isInline' => true]),
                ];
            })
            ->extraModalFooterActions([
                /*
                Tables\Actions\Action::make('pdf')
                    ->label('PDF')
                    ->action(function ($record) {
                        return response()->download(GeneratePaymentPDF::run($record));
                    })
                    ->color('gray')
                    ->icon('heroicon-o-arrow-down-tray'),
                Tables\Actions\Action::make('send')
                    ->action(function ($record, $data, $action) {
                        Mail::to($data['email'])
                            ->send(
                                new InvoicePaymentReceipt($record)
                            );
                        $action->success();
                    })
                    ->successNotificationTitle('Payment receipt sent.')
                    ->form(function ($record) {
                        return [
                            Forms\Components\TextInput::make('email')
                                ->required()
                                ->email()
                                ->default($record->invoice->customer->email),
                        ];
                    })
                    ->modalWidth('sm')
                    ->modalHeading('Send receipt')
                    ->modalSubmitActionLabel('Send')
                    ->color('success')
                    ->icon('heroicon-o-paper-airplane'),
                    */
            ])
            ->modalFooterActionsAlignment(Alignment::End)
            ->modalHeading('');
    }
}
