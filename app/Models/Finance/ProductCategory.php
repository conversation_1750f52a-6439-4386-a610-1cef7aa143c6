<?php

namespace App\Models\Finance;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'cost_account_id',
        'sales_account_id',
    ];

    public function cost_account(): BelongsTo
    {
        return $this->belongsTo(CashAccount::class, 'cost_account_id');
    }

    public function salesAccount(): BelongsTo
    {
        return $this->belongsTo(CashAccount::class, 'sales_account_id');
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'category_id');
    }
}
