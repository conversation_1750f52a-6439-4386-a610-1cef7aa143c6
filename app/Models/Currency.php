<?php

namespace App\Models;

use Filament\Forms\Components\Select;
use Illuminate\Database\Eloquent\Model;

class Currency extends Model
{
    protected $fillable = ['code', 'name', 'exchange_rate'];

    protected $casts = [
        'exchange_rate' => 'float',
    ];

    public static function getExchangeRate(string $code)
    {
        return self::query()->where('code', $code)->value('exchange_rate');
    }

    public static function getOptions()
    {
        return self::query()->pluck('name', 'code');
    }

    public static function getSelectForReport(): Select
    {
        return Select::make('currency_code')
            ->label('Currency')
            ->options(self::query()->whereIn('code', ['IDR', 'SAR'])->pluck('name', 'code'))
            ->default('IDR');
    }
}
