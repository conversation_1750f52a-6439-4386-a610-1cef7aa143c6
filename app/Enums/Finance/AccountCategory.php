<?php

namespace App\Enums\Finance;

use Filament\Support\Contracts\HasLabel;

enum AccountCategory: string implements HasLabel
{
    // Assets
    case CashBank = 'cash_bank';
    case AccountsReceivable = 'accounts_receivable';
    case Inventory = 'inventory';
    case CurrentAsset = 'current_asset';
    case NonCurrentAsset = 'non_current_asset';
    case AccumulatedDepreciation = 'accumulated_depreciation';
    // Liabilities
    case AccountsPayable = 'accounts_payable';
    case CurrentLiability = 'current_liability';
    case NonCurrentLiability = 'non_current_liability';
    // Equity
    case Equity = 'equity';
    // Operating Revenue and Expenses
    case OperatingRevenue = 'operating_revenue';
    case CostOfGoodsSold = 'cost_of_goods_sold';
    case OperatingExpense = 'operating_expense';
    case CostOfSales = 'cost_of_sales';
    case NonOperatingExpense = 'non_operating_expense';
    // Uncategorized
    case UncategorizedRevenue = 'uncategorized_revenue';
    case UncategorizedExpense = 'uncategorized_expense';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::CashBank => 'Kas & Bank',
            self::AccountsReceivable => 'Piutang Usaha',
            self::Inventory => 'Persediaan',
            self::CurrentAsset => 'Aset Lancar Lainnya',
            self::NonCurrentAsset => 'Aset Tetap',
            self::AccumulatedDepreciation => 'Akumulasi Penyusutan',
            self::AccountsPayable => 'Utang Usaha',
            self::CurrentLiability => 'Hutang Jangka Pendek',
            self::NonCurrentLiability => 'Hutang Jangka Panjang',
            self::Equity => 'Modal',
            self::OperatingRevenue => 'Pendapatan Operasional',
            self::CostOfGoodsSold => 'Harga Pokok Penjualan',
            self::OperatingExpense => 'Beban Operasional',
            self::CostOfSales => 'Beban Penjualan',
            self::NonOperatingExpense => 'Beban Administrasi dan Umum',
            self::UncategorizedRevenue => 'Pendapatan Lainnya',
            self::UncategorizedExpense => 'Beban Lainnya',
        };
    }

    /**
     * Determines if the account typically has a normal debit balance.
     *
     * In accounting, assets and expenses typically have a normal debit balance.
     * A debit increases the balance of these accounts, while a credit decreases it.
     */
    public function isNormalDebitBalance(): bool
    {
        return in_array($this, [
            ...self::groupedCases()['Aset'],
            ...self::groupedCases()['Pengeluaran'],
        ], true);
    }

    /**
     * Determines if the account typically has a normal credit balance.
     *
     * In accounting, liabilities, equity, and revenue typically have a normal credit balance.
     * A credit increases the balance of these accounts, while a debit decreases it.
     */
    public function isNormalCreditBalance(): bool
    {
        return ! $this->isNormalDebitBalance();
    }

    /**
     * Determines if the account is a nominal account.
     *
     * In accounting, nominal accounts are temporary accounts that are closed at the end of each accounting period,
     * with their net balances transferred to Retained Earnings (a real account).
     */
    public function isNominal(): bool
    {
        return in_array($this, [
            ...self::groupedCases()['Penghasilan'],
            ...self::groupedCases()['Pengeluaran'],
        ], true);
    }

    /**
     * Determines if the account is a real account.
     *
     * In accounting, assets, liabilities, and equity are real accounts which are permanent accounts that retain their balances across accounting periods.
     * They are not closed at the end of each accounting period.
     */
    public function isReal(): bool
    {
        return ! $this->isNominal();
    }

    public static function orderedCases(): array
    {
        return [
            self::CashBank,
            self::AccountsReceivable,
            self::Inventory,
            self::CurrentAsset,
            self::NonCurrentAsset,
            self::AccumulatedDepreciation,
            self::AccountsPayable,
            self::CurrentLiability,
            self::NonCurrentLiability,
            self::Equity,
            self::OperatingRevenue,
            self::CostOfGoodsSold,
            self::OperatingExpense,
            self::CostOfSales,
            self::NonOperatingExpense,
            self::UncategorizedRevenue,
            self::UncategorizedExpense,
        ];
    }

    public static function groupedCases(): array
    {
        return [
            'Aset' => [
                self::CashBank,
                self::AccountsReceivable,
                self::Inventory,
                self::CurrentAsset,
                self::NonCurrentAsset,
                self::AccumulatedDepreciation,
            ],
            'Kewajiban' => [
                self::AccountsPayable,
                self::CurrentLiability,
                self::NonCurrentLiability,
            ],
            'Ekuitas' => [
                self::Equity,
            ],
            'Penghasilan' => [
                self::OperatingRevenue,
                self::UncategorizedRevenue,
            ],
            'Pengeluaran' => [
                self::CostOfGoodsSold,
                self::OperatingExpense,
                self::CostOfSales,
                self::NonOperatingExpense,
                self::UncategorizedExpense,
            ],
        ];
    }

    public static function getOptions(): array
    {
        $grouped = [];
        foreach (self::groupedCases() as $group => $cases) {
            $grouped[$group] = [];
            foreach ($cases as $case) {
                $grouped[$group][$case->value] = $case->getLabel();
            }
        }

        return $grouped;
    }
}
