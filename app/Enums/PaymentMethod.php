<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum PaymentMethod: string implements HasLabel
{
    case Cash = 'cash';
    case BankTransfer = 'bank';
    case Deposit = 'deposit';
    case AccountsPayable = 'accounts_payable';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Cash => 'Cash',
            self::BankTransfer => 'Bank Transfer',
            self::Deposit => 'Deposit',
            self::AccountsPayable => 'Accounts Payable',
        };
    }
}
