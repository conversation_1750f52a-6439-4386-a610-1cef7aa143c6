<?php

namespace App\Console\Commands;

use App\Contracts\CurrencyHandler;
use App\Enums\Finance\AccountCategory;
use App\Models\Currency;
use App\Models\Finance\CashAccount;
use App\Models\Finance\UserCash;
use App\Models\GroupCash;
use Illuminate\Console\Command;

class SetupFinanceData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:setup-finance-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (Currency::count() == 0) {
            $currencies = [
                'SAR' => 'Saudi Riyal',
                'IDR' => 'Indonesian Rupiah',
                'USD' => 'US Dollar',
            ];
            $rates = app(CurrencyHandler::class)->getCachedExchangeRates('SAR', array_keys($currencies));

            foreach ($currencies as $code => $name) {
                Currency::create([
                    'code' => $code,
                    'name' => $name,
                    'exchange_rate' => $rates[$code] ?? 1,
                ]);
            }
        }

        /*
        $accounts = [
            // Kas & Bank
            ['code' => '1.101.001', 'category' => AccountCategory::CashBank, 'name' => 'Kas Besar'],
            ['code' => '1.101.002', 'category' => AccountCategory::CashBank, 'name' => 'Kas Kecil'],
            ['old_code' => '1.101.004', 'code' => '1.101.003', 'category' => AccountCategory::CashBank, 'name' => 'Cash Advance'],

            // Setara Kas
            ['code' => '1.102.001', 'category' => AccountCategory::CashBank, 'name' => 'Deposito Bank'],

            // Piutang Usaha
            ['old_code' => '1.104.001', 'code' => '1.103.001', 'category' => AccountCategory::AccountsReceivable, 'name' => 'Piutang Usaha'],
            ['code' => '1.103.002', 'category' => AccountCategory::AccountsReceivable, 'name' => 'Uang Muka Pembelian'],
            ['code' => '1.103.003', 'category' => AccountCategory::AccountsReceivable, 'name' => 'Debit Note'],

            // Persediaan
            ['code' => '1.104.001', 'category' => AccountCategory::Inventory, 'name' => 'Persediaan'],
            ['code' => '1.104.002', 'category' => AccountCategory::Inventory, 'name' => 'Persediaan Terkirim'],

            // Aset Lancar Lainnya
            ['code' => '1.105.001', 'category' => AccountCategory::CurrentAsset, 'name' => 'Perlengkapan Kantor'],
            ['code' => '1.105.002', 'category' => AccountCategory::CurrentAsset, 'name' => 'Sewa Gedung Dibayar di Muka'],
            ['code' => '1.105.003', 'category' => AccountCategory::CurrentAsset, 'name' => 'Asuransi Dibayar di Muka'],
            ['code' => '1.105.004', 'category' => AccountCategory::CurrentAsset, 'name' => 'PPN Masukan'],
            ['code' => '1.105.005', 'category' => AccountCategory::CurrentAsset, 'name' => 'PPh 23 Penjualan'],

            // Aset Tetap
            ['code' => '1.200.001', 'category' => AccountCategory::NonCurrentAsset, 'name' => 'Tanah'],
            ['code' => '1.200.002', 'category' => AccountCategory::NonCurrentAsset, 'name' => 'Gedung'],
            ['code' => '1.200.003', 'category' => AccountCategory::NonCurrentAsset, 'name' => 'Kendaraan'],
            ['code' => '1.200.004', 'category' => AccountCategory::NonCurrentAsset, 'name' => 'Peralatan'],
            ['code' => '1.200.005', 'category' => AccountCategory::NonCurrentAsset, 'name' => 'Inventaris Kantor'],

            // Akumulasi Depresiasi Aset Tetap
            ['code' => '1.206.001', 'category' => AccountCategory::AccumulatedDepreciation, 'name' => 'Akumulasi Penyusutan Gedung'],
            ['code' => '1.206.002', 'category' => AccountCategory::AccumulatedDepreciation, 'name' => 'Akumulasi Penyusutan Kendaraan'],
            ['code' => '1.206.003', 'category' => AccountCategory::AccumulatedDepreciation, 'name' => 'Akumulasi Penyusutan Peralatan'],
            ['code' => '1.206.004', 'category' => AccountCategory::AccumulatedDepreciation, 'name' => 'Akumulasi Penyusutan Inventaris Kantor'],

            // Utang Usaha
            ['code' => '2.101.001', 'category' => AccountCategory::AccountsPayable, 'name' => 'Utang Usaha'],
            ['code' => '2.101.002', 'category' => AccountCategory::AccountsPayable, 'name' => 'Uang Muka Penjualan'],
            ['code' => '2.101.003', 'category' => AccountCategory::AccountsPayable, 'name' => 'Credit Note'],

            // Utang Jangka Pendek Lainnya
            ['code' => '2.102.001', 'category' => AccountCategory::CurrentLiability, 'name' => 'PPN Keluaran'],
            ['code' => '2.102.002', 'category' => AccountCategory::CurrentLiability, 'name' => 'PPh 23 Pembelian'],
            ['code' => '2.102.003', 'category' => AccountCategory::CurrentLiability, 'name' => 'Utang Pembelian'],
            ['old_code' => '2.102.001', 'code' => '2.102.004', 'category' => AccountCategory::CurrentLiability, 'name' => 'Deposit Customer'],

            // Utang Jangka Panjang

            // Modal
            ['old_code' => '3.100.000', 'code' => '3.000.001', 'category' => AccountCategory::Equity, 'name' => 'Modal Saldo Awal'],
            ['old_code' => '3.101.001', 'code' => '3.000.002', 'category' => AccountCategory::Equity, 'name' => 'Laba Ditahan'],
            ['old_code' => '3.102.001', 'code' => '3.000.003', 'category' => AccountCategory::Equity, 'name' => 'Modal Saham'],

            // Pendapatan Operasional
            ['code' => '4.000.001', 'category' => AccountCategory::OperatingRevenue, 'name' => 'Penjualan Full Land Arrangement'],
            ['code' => '4.000.002', 'category' => AccountCategory::OperatingRevenue, 'name' => 'Penjualan Handling'],
            ['code' => '4.000.003', 'category' => AccountCategory::OperatingRevenue, 'name' => 'Penjualan Hotel'],
            ['code' => '4.000.004', 'category' => AccountCategory::OperatingRevenue, 'name' => 'Penjualan Visa'],
            ['code' => '4.000.005', 'category' => AccountCategory::OperatingRevenue, 'name' => 'Penjualan Tiket Pesawat'],
            ['old_code' => '4.100.000', 'code' => '4.000.009', 'category' => AccountCategory::OperatingRevenue, 'name' => 'Penjualan Lainnya'],

            // Retur Penjualan
            ['old_code' => '4.102.000', 'code' => '4.100.001', 'category' => AccountCategory::OperatingRevenue, 'name' => 'Retur Penjualan'],

            // Harga Pokok Penjualan
            ['code' => '5.000.001', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'HPP Full Land Arrangement'],
            ['old_code' => '5.100.004', 'code' => '5.000.002', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'HPP Handling'],
            ['old_code' => '5.100.003', 'code' => '5.000.003', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'HPP Hotel'],
            ['old_code' => '5.100.002', 'code' => '5.000.004', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'HPP Visa'],
            ['old_code' => '5.100.001', 'code' => '5.000.005', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'HPP Tiket Pesawat'],
            ['old_code' => '5.100.006', 'code' => '5.000.006', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'HPP Transportasi'],
            ['old_code' => '5.100.000', 'code' => '5.000.009', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'HPP Lainnya'],

            // Beban Operasional
            ['code' => '6.000.001', 'category' => AccountCategory::OperatingExpense, 'name' => 'Tiket Pesawat Runner'],
            ['code' => '6.000.002', 'category' => AccountCategory::OperatingExpense, 'name' => 'Visa Runner'],
            ['code' => '6.000.003', 'category' => AccountCategory::OperatingExpense, 'name' => 'Air Zamzam, Kurma, Albaik, Air Mineral'],
            ['code' => '6.000.004', 'category' => AccountCategory::OperatingExpense, 'name' => 'Mutawwif'],
            ['code' => '6.000.005', 'category' => AccountCategory::OperatingExpense, 'name' => 'Service Recovery'],
            ['old_code' => '6.200.000', 'code' => '6.000.009', 'category' => AccountCategory::OperatingExpense, 'name' => 'Beban Operasional Lainnya'],

            // Beban Penjualan
            ['old_code' => '6.400.000', 'code' => '7.000.001', 'category' => AccountCategory::CostOfSales, 'name' => 'Iklan dan Promosi'],
            ['old_code' => '6.100.003', 'code' => '7.000.002', 'category' => AccountCategory::CostOfSales, 'name' => 'Bonus, Komisi dan Tip'],
            ['code' => '7.000.003', 'category' => AccountCategory::CostOfSales, 'name' => 'Entertainment'],
            ['code' => '7.000.004', 'category' => AccountCategory::CostOfSales, 'name' => 'Cetakan Promosi'],
            ['code' => '7.000.009', 'category' => AccountCategory::CostOfSales, 'name' => 'Beban Penjualan Lainnya'],

            // Beban Administrasi dan Umum
            ['old_code' => '6.300.000', 'code' => '8.000.001', 'category' => AccountCategory::OperatingExpense, 'name' => 'Sewa Kantor'],
            ['old_code' => '6.100.001', 'code' => '8.000.002', 'category' => AccountCategory::OperatingExpense, 'name' => 'Gaji dan Honor'],
            ['code' => '8.000.003', 'category' => AccountCategory::OperatingExpense, 'name' => 'Tunjangan Transport'],
            ['old_code' => '6.200.006', 'code' => '8.000.004', 'category' => AccountCategory::OperatingExpense, 'name' => 'Tunjangan Makan'],
            ['code' => '8.000.005', 'category' => AccountCategory::OperatingExpense, 'name' => 'Tunjangan Perumahan'],
            ['old_code' => '6.100.002', 'code' => '8.000.006', 'category' => AccountCategory::OperatingExpense, 'name' => 'Tunjangan Hari Raya'],
            ['code' => '8.000.007', 'category' => AccountCategory::OperatingExpense, 'name' => 'Tunjangan Pendidikan'],
            ['code' => '8.000.008', 'category' => AccountCategory::OperatingExpense, 'name' => 'Tunjangan Iqomah/Umrah'],
            ['old_code' => '6.200.003', 'code' => '8.000.009', 'category' => AccountCategory::OperatingExpense, 'name' => 'Listrik'],
            ['old_code' => '6.200.002', 'code' => '8.000.010', 'category' => AccountCategory::OperatingExpense, 'name' => 'Internet, Pulsa dan Telepon'],
            ['old_code' => '6.200.011', 'code' => '8.000.011', 'category' => AccountCategory::OperatingExpense, 'name' => 'Transportasi, BBM, Tol dan Parkir'],
            ['old_code' => '6.200.008', 'code' => '8.000.012', 'category' => AccountCategory::OperatingExpense, 'name' => 'Pemeliharaan dan Perbaikan'],
            ['old_code' => '6.200.007', 'code' => '8.000.013', 'category' => AccountCategory::OperatingExpense, 'name' => 'Alat Tulis Kantor'],
            ['code' => '8.000.014', 'category' => AccountCategory::OperatingExpense, 'name' => 'Jasa Kurir, Cetakan dan Fotokopi'],
            ['code' => '8.000.015', 'category' => AccountCategory::OperatingExpense, 'name' => 'Rumah Tangga Kantor'],
            ['old_code' => '6.200.010', 'code' => '8.000.016', 'category' => AccountCategory::OperatingExpense, 'name' => 'Perjalanan Dinas'],
            ['code' => '8.000.017', 'category' => AccountCategory::OperatingExpense, 'name' => 'Sistem Aplikasi'],
            ['code' => '8.000.018', 'category' => AccountCategory::OperatingExpense, 'name' => 'Beban Penyusutan Gedung'],
            ['code' => '8.000.019', 'category' => AccountCategory::OperatingExpense, 'name' => 'Beban Penyusutan Kendaraan'],
            ['code' => '8.000.020', 'category' => AccountCategory::OperatingExpense, 'name' => 'Beban Penyusutan Peralatan'],
            ['code' => '8.000.021', 'category' => AccountCategory::OperatingExpense, 'name' => 'Beban Penyusutan Inventaris Kantor'],
            ['code' => '8.000.099', 'category' => AccountCategory::OperatingExpense, 'name' => 'Beban Administrasi dan Umum Lainnya'],

            // Pendapatan di Luar Usaha
            ['code' => '9.100.001', 'category' => AccountCategory::UncategorizedRevenue, 'name' => 'Pendapatan Jasa Giro'],
            ['code' => '9.100.002', 'category' => AccountCategory::UncategorizedRevenue, 'name' => 'Laba Selisih Kurs'],
            ['old_code' => '7.100.000', 'code' => '9.100.009', 'category' => AccountCategory::UncategorizedRevenue, 'name' => 'Pendapatan Lainnya'],

            // Beban di Luar Usaha
            ['code' => '9.200.001', 'category' => AccountCategory::UncategorizedExpense, 'name' => 'Beban Administrasi Bank'],
            ['code' => '9.200.002', 'category' => AccountCategory::UncategorizedExpense, 'name' => 'Beban Pajak Jasa Giro'],
            ['old_code' => '8.100.000', 'code' => '9.200.009', 'category' => AccountCategory::UncategorizedExpense, 'name' => 'Beban Lainnya'],
        ];
        $deletes = [
            '1.101.003', // kas kasir
            '4.101.000', // diskon penjualan
            '5.100.005', // hpp katering
            '5.100.007', // hpp perlengkapan
            '5.100.008', // hpp snack
            '5.101.000', // diskon pembelian
            '5.102.000', // retur pembelian
            '6.200.001', // biaya asuransi
            '6.200.004', // biaya air
            '6.200.005', // biaya kebersihan & keamanan
            '6.200.009', // biaya pajak
            '6.200.012', // biaya pelatihan & pengembangan
        ];

        CashAccount::query()
            ->whereIn('code', $deletes)
            ->delete();

        foreach ($accounts as $account) {
            if (isset($account['old_code']) && CashAccount::query()->where('code', $account['old_code'])->exists()) {
                CashAccount::query()->updateOrCreate([
                    'code' => $account['old_code'],
                ], [
                    'code' => $account['code'],
                    'category' => $account['category'],
                    'name' => $account['name'],
                    'is_fixed' => true,
                ]);
            } else {
                CashAccount::query()->updateOrCreate([
                    'code' => $account['code'],
                ], [
                    'category' => $account['category'],
                    'name' => $account['name'],
                    'is_fixed' => true,
                ]);
            }
        }
        */

        GroupCash::query()
            ->whereIn('division', ['handling_arr', 'handling_dep'])
            ->get()
            ->each(function ($gc) {
                UserCash::query()->create([
                    'user_id' => $gc->user_id,

                    'group_id' => $gc->group_id,
                    'category_id' => $gc->category_id,

                    'cashed_at' => $gc->cashed_at,

                    'type' => $gc->cash_in ? 'd' : 'c',
                    'amount' => $gc->cash_in ?? $gc->cash_out,
                    'currency' => $gc->currency,
                    'exchange_rate' => 1 / $gc->exchange_rate,

                    'details' => $gc->description,
                    'attachment' => $gc->attachment,
                ]);

                $gc->delete();
            });
    }
}
