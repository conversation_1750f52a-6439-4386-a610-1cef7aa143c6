<?php

namespace App\Filament\Resources\GroupResource\Pages;

use App\Enums\Enums\Gender;
use App\Filament\Resources\GroupResource;
use App\Filament\Resources\GroupResource\Widgets;
use App\Imports\ManifestImport;
use App\Models\Pilgrim;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use EightyNine\ExcelImport\ExcelImportAction;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use <PERSON><PERSON><PERSON>\FilamentInputSelectAffix\TextInputSelectAffix;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;
use Ysfkaya\FilamentPhoneInput\Tables\PhoneColumn;
use ZipArchive;

class ManageGroupManifest extends ManageRelatedRecords
{
    protected static string $resource = GroupResource::class;

    protected static string $relationship = 'pilgrims';

    protected static ?string $navigationIcon = 'heroicon-o-table-cells';

    protected static ?string $title = 'Manifest';

    public function getHeading(): string | Htmlable
    {
        return 'Manifest ' . $this->getRecordTitle();
    }

    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return auth()->user()->hasRole(['Admin', 'Operator', 'Admin Operator']);
    }

    protected function authorizeAccess(): void
    {
        abort_unless(auth()->user()->hasRole(['Admin', 'Operator', 'Admin Operator']), 403);
    }

    protected function getHeaderWidgets(): array
    {
        return [
            Widgets\ManifestOverview::make([
                'group' => $this->getOwnerRecord(),
            ]),
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\ActionGroup::make([
                ExcelImportAction::make('manifest')
                    ->color('gray')
                    ->icon('heroicon-o-table-cells')
                    ->modalIcon('heroicon-o-arrow-up-tray')
                    ->modalSubmitActionLabel('Import')
                    ->use(ManifestImport::class, group_id: $this->getOwnerRecord()->id)
                    ->afterImport(function () {
                        $this->getRecord()->logEvent('updated_manifest');
                    }),
                Actions\Action::make('photos')
                    ->icon('heroicon-o-photo')
                    ->modalIcon('heroicon-o-arrow-up-tray')
                    ->modalSubmitActionLabel('Import')
                    ->modalWidth('md')
                    ->modalAlignment('center')
                    ->modalHeading('Import Photos')
                    ->modalDescription('Import photos from zip file')
                    ->modalFooterActionsAlignment('right')
                    ->form([
                        Forms\Components\FileUpload::make('photos_zip')
                            ->label('Zip File')
                            ->acceptedFileTypes([
                                'application/zip',
                                'application/zip-compressed',
                                'application/x-zip',
                                'application/x-zip-compressed',
                                'application/octet-stream',
                                'multipart/x-zip',
                            ])
                            ->storeFiles(false)
                            ->required(),
                    ])
                    ->action(function ($data) {
                        /** @var TemporaryUploadedFile $zipTempFile */
                        $zipTempFile = $data['photos_zip'];
                        $extractPath = storage_path('app/temp_extracted_photos');

                        $zip = new ZipArchive;
                        if ($zip->open($zipTempFile->getRealPath()) === true) {
                            $zip->extractTo($extractPath);
                            $zip->close();

                            $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
                            $files = array_merge(...array_map(function ($ext) use ($extractPath) {
                                return glob($extractPath . '/*.' . $ext);
                            }, $allowedExtensions));

                            foreach ($files as $file) {
                                $filename = pathinfo($file, PATHINFO_FILENAME);

                                // Check if the filename follows the format of a passport number
                                if (! preg_match('/^[A-Z]{1,2}\d{6,7}$/', $filename)) {
                                    continue; // Skip this file if it doesn't match the passport number format
                                }

                                $pilgrim = Pilgrim::where('passport_number', $filename)->first();

                                if (! $pilgrim) {
                                    $pilgrim = Pilgrim::query()->create([
                                        'passport_number' => $filename,
                                        'fullname' => $filename,
                                    ]);
                                }

                                $newPath = 'pilgrims/photos/' . $filename . '.' . pathinfo($file, PATHINFO_EXTENSION);
                                Storage::disk('s3')->put($newPath, file_get_contents($file), 'public');
                                $pilgrim->update(['photo' => $newPath]);
                            }

                            // Clean up
                            array_map('unlink', glob("{$extractPath}/*.*"));
                            rrmdir($extractPath);

                            $zipTempFile->delete();
                            $this->getRecord()->logEvent('updated_manifest');

                            Notification::make()
                                ->title('Photos imported successfully')
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Failed to open zip file')
                                ->danger()
                                ->send();
                        }
                    }),
            ])
                ->label('Import')
                ->color('gray')
                ->button()
                ->icon('heroicon-m-chevron-down')
                ->iconPosition('after')
                ->visible(fn () => auth()->user()->hasRole(['Admin', 'Admin Operator'])),
            Actions\ActionGroup::make([
                Actions\Action::make('luggage_tags')
                    ->icon('tabler-tags')
                    ->url(route('admin.groups.print.luggage-tags', $this->getOwnerRecord()))
                    ->openUrlInNewTab()
                    ->visible((bool) count($this->getOwnerRecord()->pilgrims)),
                Actions\Action::make('roomlist')
                    ->icon('tabler-bed')
                    ->url(route('admin.groups.print.roomlist', $this->getOwnerRecord()))
                    ->openUrlInNewTab()
                    ->visible((bool) count($this->getOwnerRecord()->rooms)),
            ])
                ->label('Print')
                ->color('gray')
                ->button()
                ->icon('heroicon-m-chevron-down')
                ->iconPosition('after'),
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\FileUpload::make('photo')
                    ->label('Photo')
                    ->imageResizeTargetWidth('720')
                    ->imageResizeTargetHeight('720')
                    ->imageResizeMode('contain')
                    ->disk('s3')
                    ->directory('pilgrims/photos')
                    ->visibility('public'),
                Forms\Components\TextInput::make('passport_number')
                    ->label('Passport Number')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('national_id')
                    ->label('National ID / KTP')
                    // ->required()
                    ->maxLength(255),
                TextInputSelectAffix::make('fullname')
                    ->required()
                    ->label('Full Name')
                    ->position('prefix')
                    ->select(
                        Forms\Components\Select::make('title')
                            ->options(Pilgrim::TITLES)
                            ->placeholder('Title')
                            ->required()
                            ->default('mr')
                            ->extraFieldWrapperAttributes([
                                'style' => 'width: 6rem',
                            ])
                    )
                    ->maxLength(255),
                Forms\Components\ToggleButtons::make('gender')
                    ->options(Gender::class)
                    ->grouped(),
                Forms\Components\TextInput::make('birthplace')
                    ->label('Birth Place')
                    ->maxLength(255),
                Forms\Components\DatePicker::make('birthdate')
                    ->label('Birth Date'),
                PhoneInput::make('phone'),
                Forms\Components\Fieldset::make()
                    ->columnSpanFull()
                    ->schema([
                        Forms\Components\Toggle::make('is_tour_leader')
                            ->label('Tour Leader')
                            ->inline(),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query
                ->leftJoin('rooms', 'rooms.id', '=', 'group_pilgrim.room_id')
                ->select(
                    'pilgrims.*',
                    DB::raw('group_pilgrim.room_id as room_id'),
                    DB::raw('rooms.name as room_name'),
                    DB::raw('rooms.number as room_number'),
                    'group_pilgrim.is_tour_leader'
                )
            )
            ->recordTitleAttribute('fullname')
            ->pluralModelLabel('manifest')
            ->columns([
                Tables\Columns\TextColumn::make('index')
                    ->label('#')
                    ->rowIndex(),
                Tables\Columns\TextColumn::make('room_name')
                    ->label('Roomlist')
                    ->description(fn ($record) => $record->room_number)
                    ->sortable(),
                Tables\Columns\ImageColumn::make('photo_url')
                    ->label(''),
                BadgeableColumn::make('fullname')
                    ->label('Full Name')
                    ->searchable()
                    ->sortable()
                    ->suffixBadges([
                        Badge::make('is_tour_leader')
                            ->label('Tour Leader')
                            ->color('success')
                            ->visible(fn ($record) => $record->is_tour_leader),
                    ]),
                PhoneColumn::make('phone')
                    ->displayFormat(PhoneInputNumberType::INTERNATIONAL)
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('age')
                    ->getStateUsing(function ($record) {
                        if (! $record->birthdate) {
                            return null;
                        }
                        $group = $this->getOwnerRecord();

                        return (int) $record->birthdate->diffInYears($group->arrival_date ?? $group->created_at);
                    })
                    ->alignCenter(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Add')
                    ->after(function () {
                        $this->getRecord()->logEvent('updated_manifest');
                    })
                    ->modalHeading('Add person'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->after(function () {
                        $this->getRecord()->logEvent('updated_manifest');
                    }),
                Tables\Actions\DetachAction::make()
                    ->after(function () {
                        $this->getRecord()->logEvent('updated_manifest');
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make()
                        ->after(function () {
                            $this->getRecord()->logEvent('updated_manifest');
                        }),
                ]),
            ])
            ->defaultSort('fullname');
    }
}
