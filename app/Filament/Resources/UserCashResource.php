<?php

namespace App\Filament\Resources;

use App\Enums\ExpenseGroup;
use App\Enums\UserRole;
use App\Filament\Actions\ActivityLogTimelineTableAction;
use App\Filament\Resources\UserCashResource\Pages;
use App\Filament\Resources\UserCashResource\Widgets;
use App\Models\Bill;
use App\Models\Currency;
use App\Models\Finance\CashCategory;
use App\Models\Finance\UserCash;
use App\Models\Group;
use Carbon\Carbon;
use CodeWithDennis\FilamentSelectTree\SelectTree;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Gate;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use Spatie\Permission\Models\Role;

class UserCashResource extends Resource
{
    protected static ?string $model = UserCash::class;

    protected static ?string $modelLabel = 'transactions';

    protected static ?string $navigationGroup = 'Finance';

    protected static ?string $navigationLabel = 'Cash Advances';

    protected static ?int $navigationSort = -1;

    public static function getEloquentQuery(): Builder
    {
        $user = auth()->user();

        return static::$model::query()
            ->with(['user', 'category'])
            ->when(! $user->can('user-cashes.viewAny'), function ($query) use ($user) {
                $query
                    ->where('user_id', $user->id);
            });
    }

    public static function form(Form $form): Form
    {
        $isMutawif = auth()->user()->hasExactRoles('Mutawif');
        $isCheckInTeam = auth()->user()->hasExactRoles(UserRole::CheckInTeam->value);

        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('User')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload()
                    ->required()
                    ->visible(auth()->user()->hasRole(['Admin', 'Finance'])),
                Forms\Components\DateTimePicker::make('cashed_at')
                    ->label('Transaction Date')
                    ->default(Carbon::now())
                    ->required(),
                Group::formFieldSelectGroup()
                    ->default(fn ($livewire) => $livewire->tableFilters['group']['group_id'] ?? null),
                SelectTree::make('category_id')
                    ->label('Category')
                    ->relationship(
                        'category',
                        'name',
                        'parent_id',
                        $isMutawif
                            ? fn ($query) => $query->where('group', ExpenseGroup::Mutawif)
                            : ($isCheckInTeam
                                ? fn ($query) => $query->where('group', ExpenseGroup::HotelCheckInOut)
                                : null)
                    )
                    ->searchable()
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        $category = CashCategory::find($state);
                        if (auth()->user()->hasRole(['Admin', 'Finance'])) {
                            if ($category?->group == ExpenseGroup::VendorPayment) {
                                $set('related_type', 'bill');
                            } else {
                                $set('related_type', null);
                            }
                        }
                        $set('type', $category?->type === 'in' ? 'd' : 'c');
                    })
                    ->required(),
                Forms\Components\Hidden::make('related_type'),
                Forms\Components\Select::make('related_id')
                    ->label('Bill No.')
                    ->options(fn ($get) => Bill::query()
                        ->whereHas('items', function ($query) use ($get) {
                            $query->where('group_id', $get('group_id'));
                        })
                        ->orderByDesc('bill_number')
                        ->pluck('bill_number', 'id')
                    )
                    ->searchable()
                    ->preload()
                    ->disabled(fn () => ! auth()->user()->hasRole(['Admin', 'Finance']))
                    ->visible(fn ($get) => $get('related_type') == 'bill'),
                Forms\Components\TextInput::make('details')
                    ->autocomplete('off')
                    ->maxLength(255)
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\ToggleButtons::make('type')
                    ->options([
                        'd' => 'In',
                        'c' => 'Out',
                    ])
                    ->icons([
                        'd' => 'tabler-cash-plus',
                        'c' => 'tabler-cash-minus',
                    ])
                    ->hiddenLabel()
                    ->grouped()
                    ->default('c')
                    ->disabled()
                    ->dehydrated(),
                Forms\Components\ToggleButtons::make('currency')
                    ->options(Currency::getOptions())
                    ->default(config('finance.base_currency'))
                    ->hiddenLabel()
                    ->inline()
                    ->required()
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        $set('exchange_rate', 1 / Currency::getExchangeRate($state));
                    }),
                Forms\Components\TextInput::make('amount')
                    ->numeric()
                    ->prefix(fn ($get) => $get('currency'))
                    ->required(),
                Forms\Components\TextInput::make('exchange_rate')
                    ->numeric()
                    ->default(1)
                    ->required()
                    ->live()
                    ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency') . ' ' . round(1 / $state, 2) : null)
                    ->visible(fn ($get) => $get('currency') != 'SAR'),
                Forms\Components\FileUpload::make('attachment')
                    ->label('Bukti Transaksi')
                    ->required(fn ($get) => $get('type') == 'c')
                    ->validationMessages(['required' => 'Pengeluaran wajib menyertakan bukti transaksi'])
                    ->imageResizeTargetWidth('720')
                    ->imageResizeTargetHeight('720')
                    ->imageResizeMode('contain')
                    ->disk('s3')
                    ->directory('finance/attachments')
                    ->visibility('public'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->with(['group.customer', 'category.account']))
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->url(fn ($record) => static::getUrl('index', ['tableFilters[user_id][value]' => $record->user_id]))
                    ->visible(fn () => auth()->user()->can('user-cashes.viewAny')),
                Tables\Columns\TextColumn::make('group')
                    ->label('Group')
                    ->url(fn ($record) => $record->group ? GroupResource::getUrl('view', ['record' => $record->group]) : null)
                    ->getStateUsing(fn ($record) => $record->group?->customer?->name)
                    ->description(fn ($record) => $record->group?->name),
                Tables\Columns\TextColumn::make('cashed_at')
                    ->label('Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('details')
                    ->wrap()
                    ->description(fn ($record) => $record->category?->name ?? null)
                    ->searchable(),
                Tables\Columns\TextColumn::make('category.account.name')
                    ->visible(auth()->user()->hasRole(['Admin', 'Finance'])),
                Tables\Columns\TextColumn::make('cash_in')
                    ->label('In')
                    ->getStateUsing(fn ($record) => $record->type == 'd'
                    ? $record->amount_c
                    : null)
                    ->description(function ($record) {
                        return $record->type == 'd' && $record->currency != 'SAR'
                            ? money($record->amount, $record->currency)
                            : null;
                    })
                    ->currencyRight(),
                Tables\Columns\TextColumn::make('cash_out')
                    ->label('Out')
                    ->getStateUsing(fn ($record) => $record->type == 'c'
                    ? $record->amount_c
                    : null)
                    ->description(function ($record) {
                        return $record->type == 'c' && $record->currency != 'SAR'
                            ? money($record->amount, $record->currency)
                            : null;
                    })
                    ->currencyRight(),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachment(),
            ])
            ->filters([
                DateRangeFilter::make('cashed_at')
                    ->label('Date range')
                    ->withIndicator(),
                Tables\Filters\SelectFilter::make('user_id')
                    ->label('User')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload()
                    ->visible(fn () => auth()->user()->can('user-cashes.viewAny')),
                Group::tableFilterGroup(),
                Tables\Filters\Filter::make('category')
                    ->form([
                        SelectTree::make('id')
                            ->label('Category')
                            ->relationship('category', 'name', 'parent_id')
                            ->searchable(),
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query->when($data['id'], function ($query, $category_id) {
                            return $query->where('category_id', $category_id);
                        });
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (! $data['id']) {
                            return null;
                        }

                        return __('Category') . ': ' . CashCategory::find($data['id'])?->name;
                    }),
                Tables\Filters\SelectFilter::make('role_id')
                    ->label('User role')
                    ->options(Role::query()->pluck('name', 'id'))
                    ->searchable()
                    ->query(function ($query, $data) {
                        return $query->when(
                            $data['value'],
                            fn ($query) => $query->whereHas('user.roles', fn ($query) => $query->where('id', $data['value']))
                        );
                    })
                    ->visible(fn () => auth()->user()->can('user-cashes.viewAny')),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('verify')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(function (UserCash $record) {
                            $record->verify();
                        })
                        ->requiresConfirmation()
                        ->visible(fn ($record) => ! $record->isVerified && Gate::allows('delete', $record)),
                    Tables\Actions\ActionGroup::make([
                        Tables\Actions\EditAction::make()
                            ->visible(fn ($record) => ! $record->is_fixed)
                            ->mutateRecordDataUsing(function ($data) {
                                if (auth()->user()->hasRole(['Admin', 'Finance'])
                                    && CashCategory::find($data['category_id'])?->group == ExpenseGroup::VendorPayment) {
                                    $data['related_type'] = 'bill';
                                }

                                return $data;
                            }),
                        Tables\Actions\DeleteAction::make()
                            ->visible(fn ($record) => ! $record->is_fixed),
                    ])
                        ->dropdown(false),
                    Tables\Actions\ActionGroup::make([
                        ActivityLogTimelineTableAction::make('history')
                            ->visible(fn ($record) => Gate::allows('delete', $record)),
                    ])
                        ->dropdown(false),
                ]),
            ])
            ->filtersLayout(\Filament\Tables\Enums\FiltersLayout::AboveContent)
            ->filtersFormColumns([
                'sm' => 2,
                'lg' => 3,
                '2xl' => 4,
            ])
            ->defaultSort('cashed_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageUserCashes::route('/'),
        ];
    }

    public static function getWidgets(): array
    {
        return [
            Widgets\UserCashOverview::class,
        ];
    }
}
