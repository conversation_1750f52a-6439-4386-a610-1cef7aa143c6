<?php

declare(strict_types=1);

namespace App\Filament\Finance\Pages\Reports;

use App\Contracts\PdfService;
use App\Exports\Finance\JournalEntryExport;
use App\Models\Currency;
use App\Models\Finance\CashAccount;
use App\Models\Finance\JournalEntry;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Pages\Page;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;

class JournalEntryReport extends Page
{
    use InteractsWithFormActions;

    protected static ?string $navigationLabel = 'Journal Entry';

    protected static ?string $title = 'Journal Entry Report';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 75;

    protected static string $view = 'filament.finance.pages.reports.journal-entry-report';

    public array $filters = [];

    public ?array $data = null;

    public ?array $period = null;

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DateRangePicker::make('date_range')
                    ->label('Period')
                    ->required()
                    ->maxDate(today())
                    ->startDate(today()->startOfYear())
                    ->endDate(today())
                    ->columnSpan(['lg' => 2]),
                Currency::getSelectForReport(),
                Select::make('account_id')
                    ->label('Account (Optional)')
                    ->placeholder('All Accounts')
                    ->options(function () {
                        return CashAccount::query()
                            ->orderBy('code')
                            ->get()
                            ->mapWithKeys(function ($account) {
                                return [$account->id => $account->code . ' - ' . $account->name];
                            });
                    })
                    ->searchable(),
            ])
            ->statePath('filters')
            ->columns(3);
    }

    protected function getFormActions(): array
    {
        return [
            Actions\Action::make('filter')
                ->label('Generate Report')
                ->submit('filter'),
            Actions\ActionGroup::make([
                Actions\Action::make('download_pdf')
                    ->label('PDF')
                    ->icon('heroicon-o-document-arrow-down')
                    ->action('downloadPdf')
                    ->disabled(fn () => $this->data === null),
                Actions\Action::make('download_xlsx')
                    ->label('XLSX')
                    ->icon('phosphor-microsoft-excel-logo')
                    ->action('downloadXlsx')
                    ->disabled(fn () => $this->data === null),
            ])
                ->label('Download')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->button(),
        ];
    }

    public function filter(): void
    {
        $data = $this->form->getState();

        $dates = explode(' - ', $data['date_range'] ?? '');
        if (count($dates) == 2) {
            $start = Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay();
            $end = Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay();

            $this->period = [
                'start' => $start->format('Y-m-d'),
                'end' => $end->format('Y-m-d'),
            ];

            // Build the query for journal entries
            $query = JournalEntry::query()
                ->with(['items.account', 'transaction'])
                ->whereBetween('entry_date', [$start, $end]);

            // Filter by specific account if selected
            if (! empty($data['account_id'])) {
                $query->whereHas('items', function ($q) use ($data) {
                    $q->where('account_id', $data['account_id']);
                });
            }

            // Get all journal entries ordered by entry date
            $journalEntries = $query
                ->orderBy('entry_date')
                ->orderBy('id')
                ->get();

            // Process journal entries data
            $entriesData = [];
            $totalDebits = 0;
            $totalCredits = 0;

            foreach ($journalEntries as $entry) {
                $entryItems = [];
                $entryDebits = 0;
                $entryCredits = 0;

                foreach ($entry->items as $item) {

                    $entryItems[] = [
                        'account_id' => $item->account_id,
                        'account_code' => $item->account->code,
                        'account_name' => $item->account->name,
                        'type' => $item->type,
                        'amount' => $item->amount,
                        'debit' => $item->type === 'd' ? $item->amount : 0,
                        'credit' => $item->type === 'c' ? $item->amount : 0,
                    ];

                    if ($item->type === 'd') {
                        $entryDebits += $item->amount;
                    } else {
                        $entryCredits += $item->amount;
                    }
                }

                // Only include entries that have items (after filtering)
                if (count($entryItems) > 0) {
                    $entriesData[] = [
                        'entry_id' => $entry->id,
                        'entry_date' => $entry->entry_date,
                        'details' => $entry->details,
                        'transaction_type' => $entry->transaction ? (method_exists($entry->transaction, 'getTransactionType') ? $entry->transaction->getTransactionType() : class_basename($entry->transaction)) : null,
                        'transaction_number' => $entry->transaction ? (method_exists($entry->transaction, 'getTransactionNumber') ? $entry->transaction->getTransactionNumber() : $entry->transaction->id ?? null) : null,
                        'items' => $entryItems,
                        'entry_debits' => $entryDebits,
                        'entry_credits' => $entryCredits,
                    ];

                    $totalDebits += $entryDebits;
                    $totalCredits += $entryCredits;
                }
            }

            $this->data = [
                'currency_code' => $data['currency_code'],
                'exchange_rate' => Currency::getExchangeRate($data['currency_code']),
                'entries' => $entriesData,
                'total_debits' => $totalDebits,
                'total_credits' => $totalCredits,
                'selected_account_id' => $data['account_id'] ?? null,
            ];
        } else {
            $this->data = null;
            $this->period = null;
        }
    }

    public function downloadPdf(): void
    {
        if ($this->data === null) {
            return;
        }

        $pdfService = app(PdfService::class);

        $fileName = 'journal-entry-report-' . $this->period['start'] . '-to-' . $this->period['end'] . '.pdf';

        // Create the reports directory if it doesn't exist
        $reportsPath = storage_path('app/public/reports');
        if (! file_exists($reportsPath)) {
            mkdir($reportsPath, 0755, true);
        }

        $pdfPath = $pdfService
            ->view('pdf.journal-entry-report', [
                'data' => $this->data,
                'period' => $this->period,
            ])
            ->name($fileName)
            ->save($reportsPath);

        $this->dispatch('download-file', url('storage/reports/' . basename($pdfPath)));
    }

    public function downloadXlsx()
    {
        if ($this->data === null) {
            return;
        }

        $fileName = 'journal-entry-report-' . $this->period['start'] . '-to-' . $this->period['end'] . '.xlsx';

        return (new JournalEntryExport($this->data, $this->period))->download($fileName);
    }
}
