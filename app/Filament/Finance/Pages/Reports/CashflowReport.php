<?php

namespace App\Filament\Finance\Pages\Reports;

use App\Contracts\PdfService;
use App\Enums\Finance\AccountCategory;
use App\Models\Currency;
use App\Models\Finance\JournalEntryItem;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Pages\Page;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;

class CashflowReport extends Page
{
    use InteractsWithFormActions;

    protected static ?string $navigationLabel = 'Cash Flow';

    protected static ?string $title = 'Cash Flow Report';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 30;

    protected static string $view = 'filament.finance.pages.reports.cashflow-report';

    public array $filters = [];

    public ?array $data = null;

    public ?array $period = null;

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DateRangePicker::make('date_range')
                    ->label('Period')
                    ->required()
                    ->maxDate(today())
                    ->startDate(today()->startOfMonth())
                    ->endDate(today())
                    ->columnSpan(['lg' => 2]),
                Currency::getSelectForReport(),
            ])
            ->statePath('filters')
            ->columns(3);
    }

    protected function getFormActions(): array
    {
        return [
            Actions\Action::make('filter')
                ->label('Generate Report')
                ->submit('filter'),
            Actions\Action::make('download_pdf')
                ->label('Download PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->action('downloadPdf')
                ->disabled(fn () => $this->data === null)
                ->color('success'),
        ];
    }

    public function filter(): void
    {
        $data = $this->form->getState();

        $dates = explode(' - ', $data['date_range'] ?? '');
        if (count($dates) == 2) {
            $start = Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay();
            $end = Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay();

            $this->period = [
                'start' => $start->format('Y-m-d'),
                'end' => $end->format('Y-m-d'),
            ];

            // Get all journal entries affecting cash/bank accounts
            $cashFlows = JournalEntryItem::query()
                ->join('cash_accounts', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
                ->join('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
                ->whereBetween('journal_entries.entry_date', [$start, $end])
                ->whereIn('cash_accounts.category', [AccountCategory::CashBank])
                ->select([
                    'journal_entry_items.entry_id',
                    'journal_entries.entry_date',
                    'journal_entries.details',
                    'journal_entry_items.type',
                    'journal_entry_items.amount',
                    'cash_accounts.category as account_category',
                ])
                ->orderBy('journal_entries.entry_date')
                ->get();

            // Get the counterpart entries to categorize the cash flows
            $entries = collect();
            foreach ($cashFlows as $cashFlow) {
                $counterpart = JournalEntryItem::query()
                    ->join('cash_accounts', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
                    ->where('entry_id', $cashFlow->entry_id)
                    ->where('type', $cashFlow->type === 'd' ? 'c' : 'd')
                    ->select([
                        'cash_accounts.category as counterpart_category',
                        'cash_accounts.name as counterpart_name',
                        'journal_entry_items.amount',
                    ])
                    ->first();

                if ($counterpart) {
                    $entries->push([
                        'date' => $cashFlow->entry_date,
                        'details' => $cashFlow->details,
                        'amount' => $cashFlow->type === 'd' ? -$cashFlow->amount : $cashFlow->amount,
                        'category' => $this->categorizeTransaction($counterpart->counterpart_category),
                        'account' => $counterpart->counterpart_name,
                    ]);
                }
            }

            // Group entries by category
            $operating = $entries->where('category', 'operating')->values();
            $investing = $entries->where('category', 'investing')->values();
            $financing = $entries->where('category', 'financing')->values();

            $this->data = [
                'currency_code' => $data['currency_code'],
                'exchange_rate' => Currency::getExchangeRate($data['currency_code']),
                'operating' => [
                    'items' => $operating->toArray(),
                    'total' => $operating->sum('amount'),
                ],
                'investing' => [
                    'items' => $investing->toArray(),
                    'total' => $investing->sum('amount'),
                ],
                'financing' => [
                    'items' => $financing->toArray(),
                    'total' => $financing->sum('amount'),
                ],
                'net_change' => $operating->sum('amount') + $investing->sum('amount') + $financing->sum('amount'),
            ];
        } else {
            $this->data = null;
            $this->period = null;
        }
    }

    protected function categorizeTransaction(string | AccountCategory $category): string
    {
        if (! $category instanceof AccountCategory) {
            $category = AccountCategory::tryFrom($category);
        }

        return match ($category) {
            // Operating activities
            AccountCategory::OperatingRevenue,
            AccountCategory::CostOfGoodsSold,
            AccountCategory::OperatingExpense,
            AccountCategory::CostOfSales,
            AccountCategory::NonOperatingExpense,
            AccountCategory::UncategorizedRevenue,
            AccountCategory::UncategorizedExpense,
            AccountCategory::Inventory,
            AccountCategory::CurrentAsset,
            AccountCategory::AccountsReceivable,
            AccountCategory::CurrentLiability,
            AccountCategory::AccountsPayable => 'operating',

            // Investing activities
            AccountCategory::NonCurrentAsset => 'investing',

            // Financing activities
            AccountCategory::Equity,
            AccountCategory::NonCurrentLiability => 'financing',

            // Cash/Bank accounts should not appear as counterparts
            AccountCategory::CashBank => 'operating',
        };
    }

    public function downloadPdf(): void
    {
        if ($this->data === null) {
            return;
        }

        $pdfService = app(PdfService::class);

        $fileName = 'cashflow-report-' . $this->period['start'] . '-to-' . $this->period['end'] . '.pdf';

        // Create the reports directory if it doesn't exist
        $reportsPath = storage_path('app/public/reports');
        if (! file_exists($reportsPath)) {
            mkdir($reportsPath, 0755, true);
        }

        $pdfPath = $pdfService
            ->view('pdf.cashflow-report', [
                'data' => $this->data,
                'period' => $this->period,
            ])
            ->name($fileName)
            ->save($reportsPath);

        $this->dispatch('download-file', url('storage/reports/' . basename($pdfPath)));
    }
}
