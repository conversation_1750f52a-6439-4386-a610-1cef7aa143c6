<?php

namespace App\Filament\Finance\Pages;

use App\Settings\FinanceSettings;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;

class Settings extends SettingsPage
{
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static string $settings = FinanceSettings::class;

    protected static ?string $navigationGroup = 'Settings';

    protected static ?int $navigationSort = 1;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Invoice Settings')
                    ->description('Configure default settings for invoices')
                    ->schema([
                        Forms\Components\Textarea::make('invoice_default_terms')
                            ->label('Default Terms & Conditions')
                            ->helperText('These terms will be used as default for new invoices')
                            ->rows(15)
                            ->columnSpanFull()
                            ->required(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function getNavigationLabel(): string
    {
        return 'Settings';
    }

    public function getTitle(): string
    {
        return 'Finance Settings';
    }
}
