<?php

namespace App\Filament\Finance\Resources\OtherDepositResource\Pages;

use App\Filament\Finance\Resources\OtherDepositResource;
use Filament\Actions;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Resources\Pages\ManageRecords;

class ManageOtherDeposits extends ManageRecords
{
    use ExposesTableToWidgets;

    protected static string $resource = OtherDepositResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Add'),
        ];
    }
}
