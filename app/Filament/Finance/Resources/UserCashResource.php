<?php

namespace App\Filament\Finance\Resources;

use App\Filament\Resources\UserCashResource as ResourcesUserCashResource;

class UserCashResource extends ResourcesUserCashResource
{
    protected static ?string $navigationGroup = 'Cash Advances';

    protected static ?string $navigationLabel = 'Transactions';

    protected static ?int $navigationSort = 0;

    public static function getRouteBaseName(?string $panel = null): string
    {
        return parent::getRouteBaseName('finance');
    }
}
