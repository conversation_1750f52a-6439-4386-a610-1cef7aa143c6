<?php

namespace App\Filament\Finance\Resources\OtherPaymentResource\Pages;

use App\Filament\Finance\Resources\OtherPaymentResource;
use Filament\Actions;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Resources\Pages\ManageRecords;

class ManageOtherPayments extends ManageRecords
{
    use ExposesTableToWidgets;

    protected static string $resource = OtherPaymentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Add'),
        ];
    }
}
