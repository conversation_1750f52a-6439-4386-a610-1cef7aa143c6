<?php

namespace App\Filament\Finance\Resources\CashTransferResource\Pages;

use App\Filament\Finance\Resources\CashTransferResource;
use Filament\Actions;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Resources\Pages\ManageRecords;

class ManageCashTransfers extends ManageRecords
{
    use ExposesTableToWidgets;

    protected static string $resource = CashTransferResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Add'),
        ];
    }
}
