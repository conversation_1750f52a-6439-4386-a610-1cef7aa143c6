<?php

namespace App\Filament\Finance\Resources;

use App\Enums\Finance\AccountCategory;
use App\Filament\Finance\Resources\ProductCategoryResource\Pages;
use App\Models\Finance\CashAccount;
use App\Models\Finance\ProductCategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ProductCategoryResource extends Resource
{
    protected static ?string $model = ProductCategory::class;

    protected static ?string $modelLabel = 'category';

    protected static ?string $navigationGroup = 'Products & Services';

    protected static ?int $navigationSort = 35;

    public static function form(Form $form): Form
    {
        return $form
            ->schema(static::getFormSchema())
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query
                ->with(['cost_account', 'salesAccount'])
                ->withCount('products'))
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('cost_account.name'),
                Tables\Columns\TextColumn::make('salesAccount.name'),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->modalWidth('sm'),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => $record->products_count === 0),
            ])
            ->defaultSort('name');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageProductCategories::route('/'),
        ];
    }

    public static function getFormSchema(): array
    {
        return [
            Forms\Components\TextInput::make('name')
                ->required(),
            Forms\Components\Select::make('cost_account_id')
                ->label('Cost account')
                ->options(fn () => CashAccount::query()
                    ->whereIn('category', [AccountCategory::CostOfGoodsSold, AccountCategory::OperatingExpense, AccountCategory::CostOfSales])
                    ->orderBy('code')
                    ->get()
                    ->mapWithKeys(function ($account) {
                        return [$account->id => $account->fullname];
                    })
                )
                ->required()
                ->searchable(),
            Forms\Components\Select::make('sales_account_id')
                ->label('Sales account')
                ->options(fn () => CashAccount::query()
                    ->whereIn('category', [AccountCategory::OperatingRevenue])
                    ->orderBy('code')
                    ->get()
                    ->mapWithKeys(function ($account) {
                        return [$account->id => $account->fullname];
                    })
                )
                ->required()
                ->searchable(),
        ];
    }
}
