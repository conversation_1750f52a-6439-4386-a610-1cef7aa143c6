<?php

namespace App\Filament\Finance\Resources\CashAccountResource\Pages;

use App\Exports\ChartOfAccountsExport;
use App\Filament\Finance\Resources\CashAccountResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Maatwebsite\Excel\Facades\Excel;

class ManageCashAccounts extends ManageRecords
{
    protected static string $resource = CashAccountResource::class;

    protected static ?string $title = 'Chart of Accounts';

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('export')
                ->label('Export to Excel')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->action(function () {
                    $filename = 'chart-of-accounts-' . now()->format('Y-m-d') . '.xlsx';

                    return Excel::download(new ChartOfAccountsExport, $filename);
                }),
            Actions\CreateAction::make()
                ->modalWidth('sm'),
        ];
    }
}
